# reCAPTCHA Setup Guide

## 🚨 Current Issue
You're seeing "This reCAPTCHA is for testing purposes only" because we're using Google's test keys. Follow this guide to set up your own production keys.

## 📋 Step-by-Step Setup

### 1. Get Your reCAPTCHA Keys

1. **Visit Google reCAPTCHA Admin Console**
   - Go to: https://www.google.com/recaptcha/admin
   - Sign in with your Google account

2. **Create a New Site**
   - Click the "+" button or "Create"
   - Fill in the registration form:

   ```
   Label: Eria Software Website
   reCAPTCHA type: reCAPTCHA v2 → "I'm not a robot" Checkbox
   Domains: 
     - localhost (for development)
     - eriasoftware.com (your production domain)
     - www.eriasoftware.com (if you use www)
   ```

3. **Accept Terms and Submit**
   - Check "Accept the reCAPTCHA Terms of Service"
   - Click "Submit"

4. **Copy Your Keys**
   - **Site Key** (starts with 6L...) - This is public and goes in your frontend
   - **Secret Key** (starts with 6L...) - This is private and goes in your backend

### 2. Configure Your Environment

1. **Create Environment File**
   ```bash
   # Copy the example file
   cp .env.example .env.local
   ```

2. **Update .env.local with Your Site Key**
   ```env
   # Replace 'your_recaptcha_site_key_here' with your actual site key
   VITE_RECAPTCHA_SITE_KEY=6LcXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
   ```

### 3. Restart Your Development Server

```bash
# Stop the current server (Ctrl+C)
# Then restart
npm run dev
```

### 4. Verify Setup

1. **Check the reCAPTCHA**
   - Visit your contact page: http://localhost:8082/contact
   - The red test message should be gone
   - You should see a normal reCAPTCHA checkbox

2. **Test Form Submission**
   - Fill out the contact form
   - Complete the reCAPTCHA
   - Submit the form
   - You should receive a success message

## 🔧 For Production Deployment

### Vercel/Netlify
Add environment variables in your deployment platform:

**Vercel:**
1. Go to your project dashboard
2. Settings → Environment Variables
3. Add: `VITE_RECAPTCHA_SITE_KEY` = `your_site_key`

**Netlify:**
1. Go to Site settings
2. Environment variables
3. Add: `VITE_RECAPTCHA_SITE_KEY` = `your_site_key`

### Backend Integration (Optional)
If you want server-side verification, you'll need to:

1. **Add Secret Key to Backend**
   ```env
   RECAPTCHA_SECRET_KEY=your_secret_key_here
   ```

2. **Verify on Server**
   ```javascript
   // Example server-side verification
   const response = await fetch('https://www.google.com/recaptcha/api/siteverify', {
     method: 'POST',
     headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
     body: `secret=${secretKey}&response=${captchaToken}`
   });
   ```

## 🚨 Security Notes

- **Never commit your secret key** to version control
- **Use environment variables** for all sensitive data
- **Test thoroughly** before going live
- **Monitor your reCAPTCHA dashboard** for usage and security reports

## 📞 Need Help?

If you encounter issues:
1. Check the browser console for errors
2. Verify your domain is added to reCAPTCHA settings
3. Ensure environment variables are loaded correctly
4. Contact support: <EMAIL>

## ✅ Quick Checklist

- [ ] Created reCAPTCHA site in Google Admin Console
- [ ] Added all domains (localhost, production domain)
- [ ] Copied site key to .env.local
- [ ] Restarted development server
- [ ] Tested contact form
- [ ] Tested demo booking form
- [ ] Added environment variables to production deployment
- [ ] Verified production deployment works

Once completed, the red test message will disappear and you'll have a fully functional reCAPTCHA system protecting your forms from spam!
