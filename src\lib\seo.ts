/**
 * SEO utilities for dynamic meta tags and structured data
 */

export interface SEOData {
  title: string;
  description: string;
  keywords?: string;
  canonical?: string;
  ogTitle?: string;
  ogDescription?: string;
  ogImage?: string;
  ogType?: string;
  twitterTitle?: string;
  twitterDescription?: string;
  twitterImage?: string;
  structuredData?: object;
}

export const defaultSEO: SEOData = {
  title: "ZYKA POS - Complete Restaurant Management System | Point of Sale Software",
  description: "Transform your restaurant operations with ZYKA POS - comprehensive restaurant management software featuring advanced POS, inventory control, staff management, and WhatsApp integration. Boost efficiency and revenue today.",
  keywords: "restaurant POS software, restaurant management system, point of sale, inventory management, staff scheduling, WhatsApp integration, restaurant billing software, cloud kitchen POS, bar management system",
  canonical: "https://eriasoftware.com/",
  ogTitle: "ZYKA POS - Complete Restaurant Management System | Boost Your Restaurant Efficiency",
  ogDescription: "Streamline your restaurant operations with ZYKA POS's all-in-one management platform. Features advanced billing, inventory tracking, staff management, and seamless integrations to grow your business.",
  ogImage: "https://eriasoftware.com/og-image.png",
  ogType: "website",
  twitterTitle: "ZYKA POS - Complete Restaurant Management System",
  twitterDescription: "Transform your restaurant operations with ZYKA POS - comprehensive restaurant management software.",
  twitterImage: "https://eriasoftware.com/twitter-image.png",
  structuredData: {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "Eria Software Solutions & Services Pvt Ltd",
    "url": "https://eriasoftware.com",
    "logo": "https://eriasoftware.com/logo.png",
    "description": "Leading provider of restaurant management software and POS systems",
    "contactPoint": {
      "@type": "ContactPoint",
      "telephone": "+91-**********",
      "contactType": "customer service",
      "email": "<EMAIL>"
    },
    "address": {
      "@type": "PostalAddress",
      "addressLocality": "Mumbai",
      "addressRegion": "Maharashtra",
      "addressCountry": "IN"
    },
    "sameAs": [
      "https://www.eriasoftware.com"
    ]
  }
};

export const pageSEO: Record<string, SEOData> = {
  "/": defaultSEO,
  "/point-of-sale": {
    title: "Point of Sale System | ZYKA POS Restaurant Software",
    description: "Advanced POS system for restaurants with intuitive interface, fast billing, payment processing, and real-time reporting. Streamline your restaurant operations with ZYKA POS.",
    keywords: "restaurant POS system, point of sale software, restaurant billing, payment processing, POS terminal, restaurant technology",
    canonical: "https://eriasoftware.com/point-of-sale",
    ogTitle: "Point of Sale System | ZYKA POS Restaurant Software",
    ogDescription: "Advanced POS system for restaurants with intuitive interface, fast billing, and real-time reporting.",
    structuredData: {
      "@context": "https://schema.org",
      "@type": "SoftwareApplication",
      "name": "ERIA POS System",
      "applicationCategory": "BusinessApplication",
      "description": "Advanced point of sale system for restaurants"
    }
  },
  "/inventory": {
    title: "Restaurant Inventory Management | ZYKA POS Software",
    description: "Comprehensive inventory management system for restaurants. Track stock levels, manage suppliers, automate reordering, and reduce waste with ZYKA POS inventory features.",
    keywords: "restaurant inventory management, stock control, supplier management, inventory tracking, restaurant supplies, food cost control",
    canonical: "https://eriasoftware.com/inventory",
    ogTitle: "Restaurant Inventory Management | ZYKA POS Software",
    ogDescription: "Comprehensive inventory management system for restaurants with automated tracking and supplier management."
  },
  "/website-development": {
    title: "Restaurant Website Development | Professional Web Design Services",
    description: "Custom restaurant website development services. Mobile-responsive designs, online ordering integration, SEO optimization, and modern web solutions for restaurants.",
    keywords: "restaurant website development, web design, online ordering website, restaurant web development, mobile responsive design, SEO optimization",
    canonical: "https://eriasoftware.com/website-development",
    ogTitle: "Restaurant Website Development | Professional Web Design Services",
    ogDescription: "Custom restaurant website development with mobile-responsive designs and online ordering integration."
  },
  "/app-development": {
    title: "Restaurant Mobile App Development | iOS & Android Apps",
    description: "Custom mobile app development for restaurants. Native iOS and Android apps with online ordering, push notifications, loyalty programs, and seamless user experience.",
    keywords: "restaurant mobile app development, iOS app development, Android app development, online ordering app, restaurant app, mobile ordering",
    canonical: "https://eriasoftware.com/app-development",
    ogTitle: "Restaurant Mobile App Development | iOS & Android Apps",
    ogDescription: "Custom mobile app development for restaurants with native iOS and Android solutions."
  },
  "/freshservice-crm": {
    title: "FreshService CRM Integration | Customer Relationship Management",
    description: "Integrate FreshService CRM with your restaurant operations. Manage customer relationships, support tickets, and enhance customer experience with professional CRM solutions.",
    keywords: "FreshService CRM, customer relationship management, restaurant CRM, customer support, help desk, service management",
    canonical: "https://eriasoftware.com/freshservice-crm",
    ogTitle: "FreshService CRM Integration | Customer Relationship Management",
    ogDescription: "Professional CRM integration services for restaurants using FreshService platform."
  },
  "/about-us": {
    title: "About Eria Software | Restaurant Technology Solutions Company",
    description: "Learn about Eria Software Solutions & Services Pvt Ltd - leading provider of restaurant technology solutions, POS systems, and business management software since our founding.",
    keywords: "Eria Software, restaurant technology company, POS software company, business solutions, restaurant management, software development",
    canonical: "https://eriasoftware.com/about-us",
    ogTitle: "About Eria Software | Restaurant Technology Solutions Company",
    ogDescription: "Leading provider of restaurant technology solutions and POS systems for modern businesses."
  },
  "/contact": {
    title: "Contact Eria Software | Get Support & Demo | Restaurant POS",
    description: "Contact Eria Software for demos, support, and consultations. Phone: +91-**********, Email: <EMAIL>. Get personalized solutions for your restaurant.",
    keywords: "contact Eria Software, restaurant POS support, demo request, customer support, consultation, restaurant technology help",
    canonical: "https://eriasoftware.com/contact",
    ogTitle: "Contact Eria Software | Get Support & Demo",
    ogDescription: "Get in touch for demos, support, and personalized restaurant technology solutions."
  },
  "/support": {
    title: "24/7 Support | ZYKA POS Help Center | Restaurant Software Support",
    description: "Get 24/7 support for ZYKA POS restaurant software. Access documentation, training resources, troubleshooting guides, and expert assistance for your restaurant operations.",
    keywords: "ZYKA POS support, restaurant software help, 24/7 support, technical assistance, user guide, troubleshooting, training",
    canonical: "https://eriasoftware.com/support",
    ogTitle: "24/7 Support | ZYKA POS Help Center",
    ogDescription: "Comprehensive support and training resources for ZYKA POS restaurant software."
  },
  "/blog": {
    title: "Restaurant Technology Blog | Industry Insights | Eria Software",
    description: "Stay updated with latest restaurant technology trends, business tips, and industry insights. Expert articles on POS systems, inventory management, and restaurant operations.",
    keywords: "restaurant technology blog, restaurant business tips, POS system insights, restaurant management, industry trends, business operations",
    canonical: "https://eriasoftware.com/blog",
    ogTitle: "Restaurant Technology Blog | Industry Insights",
    ogDescription: "Expert insights and tips on restaurant technology, business operations, and industry trends."
  },
  "/demo": {
    title: "Book a Demo | ZYKA POS Restaurant Software | Free Consultation",
    description: "Schedule a free personalized demo of ZYKA POS restaurant management software. See how our POS system can transform your restaurant operations. Book your 30-minute session today.",
    keywords: "ZYKA POS demo, restaurant software demo, POS system demonstration, free consultation, restaurant technology demo, book demo",
    canonical: "https://eriasoftware.com/demo",
    ogTitle: "Book a Demo | ZYKA POS Restaurant Software",
    ogDescription: "Schedule a free personalized demo and see how ZYKA POS can transform your restaurant operations.",
    structuredData: {
      "@context": "https://schema.org",
      "@type": "Service",
      "name": "ZYKA POS Demo",
      "description": "Free personalized demonstration of restaurant POS software",
      "provider": {
        "@type": "Organization",
        "name": "Eria Software Solutions & Services Pvt Ltd"
      }
    }
  },
  "/privacy-policy": {
    title: "Privacy Policy | Eria Software | Data Protection & GDPR Compliance",
    description: "Eria Software's privacy policy explaining how we collect, use, and protect your personal data. GDPR and Indian data protection law compliant. Learn about your privacy rights.",
    keywords: "privacy policy, data protection, GDPR compliance, personal data, privacy rights, data processing, Eria Software privacy",
    canonical: "https://eriasoftware.com/privacy-policy",
    ogTitle: "Privacy Policy | Eria Software Data Protection",
    ogDescription: "Learn how Eria Software protects your personal data and respects your privacy rights."
  },
  "/terms-of-service": {
    title: "Terms of Service | Eria Software | Legal Terms & Conditions",
    description: "Terms of service for Eria Software's restaurant POS and management solutions. Legal terms, user responsibilities, and service conditions for our software products.",
    keywords: "terms of service, legal terms, terms and conditions, user agreement, software license, service terms, Eria Software terms",
    canonical: "https://eriasoftware.com/terms-of-service",
    ogTitle: "Terms of Service | Eria Software Legal Terms",
    ogDescription: "Legal terms and conditions for using Eria Software's restaurant management solutions."
  },
  "/cookie-policy": {
    title: "Cookie Policy | Eria Software | GDPR & Privacy Compliance",
    description: "Eria Software's cookie policy explaining how we use cookies and tracking technologies. GDPR compliant with granular consent options and privacy controls.",
    keywords: "cookie policy, cookies, tracking, GDPR compliance, privacy controls, consent management, data protection, web analytics",
    canonical: "https://eriasoftware.com/cookie-policy",
    ogTitle: "Cookie Policy | Eria Software Privacy Controls",
    ogDescription: "Learn about our cookie usage and manage your privacy preferences with granular controls."
  },
  "/accessibility-statement": {
    title: "Accessibility Statement | Eria Software | WCAG 2.1 AA Compliant",
    description: "Eria Software's commitment to digital accessibility. WCAG 2.1 Level AA compliant website with features for users with disabilities. Learn about our accessibility efforts.",
    keywords: "accessibility statement, WCAG compliance, digital accessibility, disability access, screen reader support, keyboard navigation, inclusive design",
    canonical: "https://eriasoftware.com/accessibility-statement",
    ogTitle: "Accessibility Statement | Eria Software Digital Inclusion",
    ogDescription: "Our commitment to digital accessibility and inclusive design for all users."
  },
  "/security-policy": {
    title: "Security Policy | Eria Software | Data Protection & Cybersecurity",
    description: "Eria Software's comprehensive security policy covering data protection, encryption, compliance standards, and vulnerability reporting procedures.",
    keywords: "security policy, data protection, cybersecurity, encryption, ISO 27001, SOC 2, vulnerability reporting, information security",
    canonical: "https://eriasoftware.com/security-policy",
    ogTitle: "Security Policy | Eria Software Data Protection",
    ogDescription: "Comprehensive security measures and policies protecting your data and our systems."
  }
};

export const updatePageSEO = (path: string): void => {
  const seoData = pageSEO[path] || defaultSEO;
  
  // Update title
  document.title = seoData.title;
  
  // Update meta tags
  updateMetaTag('description', seoData.description);
  if (seoData.keywords) updateMetaTag('keywords', seoData.keywords);
  
  // Update canonical link
  updateCanonicalLink(seoData.canonical || `https://eriasoftware.com${path}`);
  
  // Update Open Graph tags
  updateMetaProperty('og:title', seoData.ogTitle || seoData.title);
  updateMetaProperty('og:description', seoData.ogDescription || seoData.description);
  updateMetaProperty('og:url', seoData.canonical || `https://eriasoftware.com${path}`);
  if (seoData.ogImage) updateMetaProperty('og:image', seoData.ogImage);
  if (seoData.ogType) updateMetaProperty('og:type', seoData.ogType);
  
  // Update Twitter tags
  updateMetaName('twitter:title', seoData.twitterTitle || seoData.title);
  updateMetaName('twitter:description', seoData.twitterDescription || seoData.description);
  if (seoData.twitterImage) updateMetaName('twitter:image', seoData.twitterImage);
  
  // Update structured data
  if (seoData.structuredData) {
    updateStructuredData(seoData.structuredData);
  }
};

const updateMetaTag = (name: string, content: string): void => {
  let meta = document.querySelector(`meta[name="${name}"]`) as HTMLMetaElement;
  if (!meta) {
    meta = document.createElement('meta');
    meta.name = name;
    document.head.appendChild(meta);
  }
  meta.content = content;
};

const updateMetaProperty = (property: string, content: string): void => {
  let meta = document.querySelector(`meta[property="${property}"]`) as HTMLMetaElement;
  if (!meta) {
    meta = document.createElement('meta');
    meta.setAttribute('property', property);
    document.head.appendChild(meta);
  }
  meta.content = content;
};

const updateMetaName = (name: string, content: string): void => {
  let meta = document.querySelector(`meta[name="${name}"]`) as HTMLMetaElement;
  if (!meta) {
    meta = document.createElement('meta');
    meta.name = name;
    document.head.appendChild(meta);
  }
  meta.content = content;
};

const updateCanonicalLink = (href: string): void => {
  let link = document.querySelector('link[rel="canonical"]') as HTMLLinkElement;
  if (!link) {
    link = document.createElement('link');
    link.rel = 'canonical';
    document.head.appendChild(link);
  }
  link.href = href;
};

const updateStructuredData = (data: object): void => {
  // Remove existing structured data
  const existing = document.querySelector('script[type="application/ld+json"]');
  if (existing) {
    existing.remove();
  }
  
  // Add new structured data
  const script = document.createElement('script');
  script.type = 'application/ld+json';
  script.textContent = JSON.stringify(data);
  document.head.appendChild(script);
};
