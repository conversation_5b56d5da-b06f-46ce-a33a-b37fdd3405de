import { Button } from "@/components/ui/button";

const ElevateSection = () => {
  return (
    <section className="w-full py-16 lg:py-24 bg-background">
      <div className="container mx-auto px-6 lg:px-8">
        <div className="text-center max-w-4xl mx-auto space-y-8">
          <h2 className="text-4xl lg:text-6xl font-bold text-foreground leading-tight">
            प्रत्येक अतिथी संवादाचे महसूल वाढीमध्ये रूपांतर करा
          </h2>

          <div className="text-3xl lg:text-4xl font-bold text-primary">
            निर्बाध कामकाज, अपवादात्मक अनुभव.
          </div>

          <p className="text-lg lg:text-xl text-muted-foreground leading-relaxed max-w-3xl mx-auto">
            Zyka POS जेवणाला संस्मरणीय बनवणारा मानवी स्पर्श जपून ठेवताना जटिल कामकाज बुद्धिमानपणे स्वयंचलित करून रेस्टॉरंट व्यवस्थापनात क्रांती घडवून आणते.
            आमची प्रगत प्रणाली ऑर्डर प्रक्रिया वेळ 40% कमी करते, चुका कमी करते आणि रिअल-टाइम अंतर्दृष्टी प्रदान करते जी आपल्याला फायदेशीर निर्णय घेण्यास मदत करते.
            जेव्हा आपले कामकाज सुरळीत चालते, तेव्हा आपली टीम पहिल्यांदा येणार्‍या पाहुण्यांना निष्ठावान ग्राहकांमध्ये बदलणारे असाधारण क्षण निर्माण करण्यावर लक्ष केंद्रित करू शकते.
          </p>

          <Button
            variant="hero"
            size="lg"
            className="text-lg px-8 py-6 bg-blue-600 text-white hover:bg-blue-700"
            onClick={() => window.open('https://zykapos.eriasoftware.com', '_blank')}
          >
            डेमो घ्या
          </Button>
        </div>
      </div>
    </section>
  );
};

export default ElevateSection;