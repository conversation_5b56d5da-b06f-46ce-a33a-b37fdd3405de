import { useEffect, useState, useRef } from "react";
import ReCA<PERSON><PERSON><PERSON> from "react-google-recaptcha";
import { getCal<PERSON><PERSON> } from "@calcom/embed-react";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { useToast } from "@/hooks/use-toast";
import withPageLoader from "@/components/withPageLoader";
import { Link } from "react-router-dom";
import {
  Calendar,
  Clock,
  Users,
  CheckCircle,
  Video,
  Phone,
  MessageSquare,
  Star,
  ArrowRight,
  Shield,
  AlertCircle
} from "lucide-react";

const Demo = () => {
  const { toast } = useToast();
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);
  const [captchaValue, setCaptchaValue] = useState<string | null>(null);
  const [dataProcessingConsent, setDataProcessingConsent] = useState(false);
  const [marketingConsent, setMarketingConsent] = useState(false);
  const recaptchaRef = useRef<ReCAPTCHA>(null);

  const handleDemoBooking = () => {
    if (!captchaValue) {
      toast({
        title: "CAPTCHA verification required",
        description: "Please complete the CAPTCHA verification before booking a demo.",
        variant: "destructive",
      });
      return;
    }

    if (!dataProcessingConsent) {
      toast({
        title: "Consent required",
        description: "Please consent to data processing to book a demo.",
        variant: "destructive",
      });
      return;
    }

    // CAPTCHA and consent are valid, proceed with Cal.com booking
    // The actual booking will be handled by Cal.com's click event
  };

  const scrollToBooking = () => {
    const bookingSection = document.getElementById('booking-section');
    if (bookingSection) {
      bookingSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  useEffect(() => {
    (async function () {
      const cal = await getCalApi({"namespace":"eria-software-restaurant-pos-demo"});
      cal("ui", {"hideEventTypeDetails":true,"layout":"week_view"});

      // Listen for booking events
      cal("on", {
        action: "bookingSuccessful",
        callback: (e: any) => {
          setShowSuccessMessage(true);
          toast({
            title: "Demo booked successfully!",
            description: "You'll receive a confirmation email shortly. We're excited to show you Eria Software!",
          });
        }
      });

      cal("on", {
        action: "bookingFailed",
        callback: (e: any) => {
          toast({
            title: "Booking failed",
            description: "Please try again or contact us directly at +91 9604069989",
            variant: "destructive",
          });
        }
      });
    })();
  }, [toast]);

  const demoFeatures = [
    {
      icon: Video,
      title: "Live Demo Session",
      description: "Interactive walkthrough of our restaurant POS system with a product expert"
    },
    {
      icon: Users,
      title: "Personalized Consultation",
      description: "Tailored discussion about your specific restaurant needs and requirements"
    },
    {
      icon: CheckCircle,
      title: "Feature Overview",
      description: "Complete overview of POS, inventory management, and reporting features"
    },
    {
      icon: MessageSquare,
      title: "Q&A Session",
      description: "Get all your questions answered by our technical experts"
    }
  ];

  const testimonials = [
    {
      name: "Rajesh Kumar",
      role: "Restaurant Owner",
      rating: 5,
      comment: "The demo was incredibly helpful. The team showed exactly how the system would work for my restaurant."
    },
    {
      name: "Priya Sharma",
      role: "Cafe Manager", 
      rating: 5,
      comment: "Professional presentation and great support. The booking process was seamless."
    },
    {
      name: "Amit Patel",
      role: "Food Chain Owner",
      rating: 5,
      comment: "Excellent demo experience. They understood our multi-location requirements perfectly."
    }
  ];

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      {/* Hero Section */}
      <section className="w-full bg-gradient-hero py-16 lg:py-24">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="text-center max-w-4xl mx-auto space-y-8">
            <Badge variant="secondary" className="text-sm font-medium bg-gray-100 text-gray-700 rounded-full px-4 py-2">
              Book Your Demo
            </Badge>
            
            <h1 className="text-4xl lg:text-6xl font-bold text-foreground leading-tight">
              See Eria Software in Action
            </h1>
            
            <p className="text-lg text-muted-foreground leading-relaxed max-w-3xl mx-auto">
              Schedule a personalized demo of our restaurant POS system and discover how we can 
              transform your business operations with cutting-edge technology.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <button
                onClick={scrollToBooking}
                className="inline-flex items-center justify-center px-8 py-4 text-lg font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md transition-colors duration-200"
              >
                <Calendar className="w-5 h-5 mr-2" />
                Schedule Demo Now
              </button>

              <Button
                variant="outline"
                size="lg"
                className="text-lg px-8 py-4 border-2 border-gray-300 text-gray-700 hover:bg-gray-50"
              >
                <Phone className="w-5 h-5 mr-2" />
                Call for Instant Demo
              </Button>
            </div>

            <div className="pt-4 text-sm text-muted-foreground">
              ✓ 30-minute personalized session • ✓ No commitment required • ✓ Expert guidance
            </div>
          </div>
        </div>
      </section>

      {/* What to Expect */}
      <section className="w-full py-16 lg:py-24 bg-muted/30">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-foreground mb-4">
              What to Expect in Your Demo
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Our product experts will guide you through a comprehensive demonstration 
              tailored to your restaurant's specific needs.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {demoFeatures.map((feature, index) => (
              <Card 
                key={index} 
                className="border-0 shadow-soft hover:shadow-lg transition-all duration-300 hover:scale-105 bg-background text-center"
              >
                <CardContent className="p-6">
                  <div className="space-y-4">
                    <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto">
                      <feature.icon className="w-8 h-8 text-white" />
                    </div>
                    
                    <h3 className="text-xl font-semibold text-foreground">
                      {feature.title}
                    </h3>
                    
                    <p className="text-muted-foreground leading-relaxed text-sm">
                      {feature.description}
                    </p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Demo Booking Section */}
      <section id="booking-section" className="w-full py-16 lg:py-24 bg-background">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl lg:text-4xl font-bold text-foreground mb-4">
                Choose Your Preferred Time
              </h2>
              <p className="text-lg text-muted-foreground">
                Select a convenient time slot for your personalized demo session.
              </p>
            </div>

            <Card className="border-0 shadow-soft bg-background">
              <CardContent className="p-8">
                <div className="text-center space-y-6">
                  <div className="flex items-center justify-center space-x-4 text-muted-foreground">
                    <div className="flex items-center">
                      <Clock className="w-5 h-5 mr-2" />
                      <span>30 minutes</span>
                    </div>
                    <div className="flex items-center">
                      <Video className="w-5 h-5 mr-2" />
                      <span>Online meeting</span>
                    </div>
                    <div className="flex items-center">
                      <Users className="w-5 h-5 mr-2" />
                      <span>1-on-1 session</span>
                    </div>
                  </div>

                  {/* Consent Checkboxes */}
                  <div className="space-y-3 p-4 bg-gray-50 rounded-lg border mb-6">
                    <div className="space-y-3">
                      <div className="flex items-start space-x-3">
                        <Checkbox
                          id="demoDataProcessingConsent"
                          checked={dataProcessingConsent}
                          onCheckedChange={(checked) => setDataProcessingConsent(checked as boolean)}
                          className="mt-0.5"
                        />
                        <label htmlFor="demoDataProcessingConsent" className="text-sm text-gray-700 cursor-pointer">
                          <span className="text-red-600">*</span> I agree to data processing for demo scheduling and follow-up.
                          See our{" "}
                          <Link to="/privacy-policy" className="text-blue-600 hover:underline">
                            Privacy Policy
                          </Link>
                          .
                        </label>
                      </div>

                      <div className="flex items-start space-x-3">
                        <Checkbox
                          id="demoMarketingConsent"
                          checked={marketingConsent}
                          onCheckedChange={(checked) => setMarketingConsent(checked as boolean)}
                          className="mt-0.5"
                        />
                        <label htmlFor="demoMarketingConsent" className="text-sm text-gray-700 cursor-pointer">
                          I'd like to receive product updates after the demo (optional).
                        </label>
                      </div>
                    </div>

                    <div className="text-xs text-gray-600 bg-gray-100 p-2 rounded">
                      <Shield className="w-3 h-3 inline mr-1" />
                      You can withdraw consent anytime at{" "}
                      <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline">
                        <EMAIL>
                      </a>
                    </div>
                  </div>

                  {/* reCAPTCHA */}
                  <div className="flex justify-center mb-6">
                    <ReCAPTCHA
                      ref={recaptchaRef}
                      sitekey={import.meta.env.VITE_RECAPTCHA_SITE_KEY || "6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI"}
                      onChange={(value) => setCaptchaValue(value)}
                      onExpired={() => setCaptchaValue(null)}
                    />
                  </div>

                  <button
                    data-cal-namespace="eria-software-restaurant-pos-demo"
                    data-cal-link="eriasoftware/eria-software-restaurant-pos-demo"
                    data-cal-config='{"layout":"week_view"}'
                    onClick={handleDemoBooking}
                    disabled={!captchaValue || !dataProcessingConsent}
                    className="w-full max-w-md mx-auto inline-flex items-center justify-center px-8 py-4 text-lg font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed rounded-md transition-colors duration-200"
                  >
                    <Calendar className="w-5 h-5 mr-2" />
                    Book Your Demo Session
                  </button>

                  <p className="text-sm text-muted-foreground">
                    Available Monday to Saturday, 9:00 AM - 6:00 PM IST
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Success Message */}
      {showSuccessMessage && (
        <section className="w-full py-16 bg-green-50">
          <div className="container mx-auto px-6 lg:px-8">
            <div className="max-w-2xl mx-auto text-center">
              <Card className="border-green-200 bg-white shadow-lg">
                <CardContent className="p-8">
                  <div className="space-y-4">
                    <div className="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mx-auto">
                      <CheckCircle className="w-8 h-8 text-white" />
                    </div>

                    <h3 className="text-2xl font-bold text-green-800">
                      Demo Booked Successfully!
                    </h3>

                    <p className="text-green-700">
                      Thank you for booking a demo with Eria Software. You'll receive a confirmation
                      email shortly with all the details. Our team is excited to show you how our
                      solutions can transform your business!
                    </p>

                    <div className="pt-4 space-y-2 text-sm text-green-600">
                      <p>✓ Confirmation email sent</p>
                      <p>✓ Calendar invite included</p>
                      <p>✓ Demo materials prepared</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>
      )}

      {/* Customer Testimonials */}
      <section className="w-full py-16 lg:py-24 bg-muted/20">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-foreground mb-4">
              What Our Customers Say
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Hear from restaurant owners who have experienced our demo sessions.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <Card
                key={index}
                className="border-0 shadow-soft bg-background"
              >
                <CardContent className="p-6">
                  <div className="space-y-4">
                    <div className="flex items-center space-x-1">
                      {[...Array(testimonial.rating)].map((_, i) => (
                        <Star key={i} className="w-5 h-5 fill-yellow-400 text-yellow-400" />
                      ))}
                    </div>

                    <p className="text-muted-foreground leading-relaxed italic">
                      "{testimonial.comment}"
                    </p>

                    <div className="pt-4 border-t border-gray-100">
                      <p className="font-semibold text-foreground">
                        {testimonial.name}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        {testimonial.role}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="w-full py-16 lg:py-24 bg-background">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl lg:text-4xl font-bold text-foreground mb-8">
              Why Book a Demo with Eria Software?
            </h2>

            <div className="grid md:grid-cols-2 gap-8 text-left">
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <CheckCircle className="w-6 h-6 text-green-600 mt-1 flex-shrink-0" />
                  <div>
                    <h3 className="font-semibold text-foreground mb-1">No Pressure Sales</h3>
                    <p className="text-muted-foreground">Educational session focused on understanding your needs</p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <CheckCircle className="w-6 h-6 text-green-600 mt-1 flex-shrink-0" />
                  <div>
                    <h3 className="font-semibold text-foreground mb-1">Expert Guidance</h3>
                    <p className="text-muted-foreground">Learn from experienced restaurant technology specialists</p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <CheckCircle className="w-6 h-6 text-green-600 mt-1 flex-shrink-0" />
                  <div>
                    <h3 className="font-semibold text-foreground mb-1">Customized Presentation</h3>
                    <p className="text-muted-foreground">Demo tailored to your restaurant type and requirements</p>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <CheckCircle className="w-6 h-6 text-green-600 mt-1 flex-shrink-0" />
                  <div>
                    <h3 className="font-semibold text-foreground mb-1">Flexible Scheduling</h3>
                    <p className="text-muted-foreground">Choose a time that works best for your schedule</p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <CheckCircle className="w-6 h-6 text-green-600 mt-1 flex-shrink-0" />
                  <div>
                    <h3 className="font-semibold text-foreground mb-1">Immediate Support</h3>
                    <p className="text-muted-foreground">Get answers to all your questions in real-time</p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <CheckCircle className="w-6 h-6 text-green-600 mt-1 flex-shrink-0" />
                  <div>
                    <h3 className="font-semibold text-foreground mb-1">Free Consultation</h3>
                    <p className="text-muted-foreground">No cost for the demo session and initial consultation</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section className="w-full py-16 lg:py-24 bg-black">
        <div className="container mx-auto px-6 lg:px-8 text-center">
          <div className="max-w-3xl mx-auto space-y-8">
            <h2 className="text-3xl lg:text-5xl font-bold text-white">
              Ready to Transform Your Restaurant?
            </h2>

            <p className="text-lg lg:text-xl text-white/90 leading-relaxed">
              Join 50+ restaurants that have already experienced the power of Eria Software.
              Book your demo today and see the difference.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <button
                data-cal-namespace="eria-software-restaurant-pos-demo"
                data-cal-link="eriasoftware/eria-software-restaurant-pos-demo"
                data-cal-config='{"layout":"week_view"}'
                className="inline-flex items-center justify-center px-8 py-4 text-lg font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md transition-colors duration-200"
              >
                <Calendar className="w-5 h-5 mr-2" />
                Schedule Your Demo
              </button>

              <Button
                variant="outline"
                size="lg"
                className="text-lg px-8 py-4 bg-transparent border-2 border-white text-white hover:bg-white hover:text-black"
              >
                <Phone className="w-5 h-5 mr-2" />
                Call +91 9604069989
              </Button>
            </div>

            <div className="pt-8 text-sm text-white/80">
              ✓ Available Monday-Saturday • ✓ 30-minute sessions • ✓ Expert consultation • ✓ No commitment required
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default withPageLoader(Demo, {
  loadingText: "Loading Demo Page...",
  minLoadingTime: 1200
});
