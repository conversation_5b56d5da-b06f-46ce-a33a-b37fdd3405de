import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import withPageLoader from "@/components/withPageLoader";

const TermsOfService = () => {
  const sections = [
    {
      title: "Acceptance of Terms",
      content: [
        "By accessing and using ERIA POS software and services, you accept and agree to be bound by these Terms of Service",
        "If you do not agree to these terms, you may not use our services",
        "These terms apply to all users, including customers, visitors, and others who access the service",
        "We reserve the right to update these terms at any time with notice to users"
      ]
    },
    {
      title: "Service Description",
      content: [
        "ERIA POS is a comprehensive restaurant management software solution",
        "Services include point-of-sale systems, inventory management, analytics, and related features",
        "We provide software licensing, technical support, and maintenance services",
        "Additional services may include custom development, training, and consulting"
      ]
    },
    {
      title: "User Responsibilities",
      content: [
        "Provide accurate and complete information when creating your account or submitting forms",
        "Ensure you have the right to provide any personal information you submit to us",
        "Provide valid consent for data processing when submitting contact or demo forms",
        "Maintain the security of your account credentials and notify us of any unauthorized access",
        "Use the service in compliance with applicable laws and regulations",
        "Not attempt to reverse engineer, modify, or distribute our software without permission",
        "Pay all fees and charges associated with your use of the service",
        "Understand and manage your cookie preferences through our provided tools"
      ]
    },
    {
      title: "Cookies and Tracking Technologies",
      content: [
        "By using our services, you acknowledge our use of cookies and similar technologies",
        "Essential cookies are automatically placed and cannot be disabled as they are necessary for service functionality",
        "Optional cookies require your explicit consent and can be managed through our preference center",
        "You have the right to withdraw consent for optional cookies at any time",
        "Disabling certain cookies may limit functionality and your ability to use some features",
        "Third-party cookies may be used subject to your consent and their respective privacy policies"
      ]
    },
    {
      title: "Payment Terms",
      content: [
        "Subscription fees are billed in advance on a monthly or annual basis",
        "All fees are non-refundable except as expressly stated in our refund policy",
        "We reserve the right to change our pricing with 30 days' notice",
        "Late payments may result in service suspension or termination",
        "You are responsible for all taxes associated with your use of the service"
      ]
    },
    {
      title: "Intellectual Property",
      content: [
        "ERIA POS software and all related materials are owned by Eria Software Solutions & Services Pvt Ltd",
        "You are granted a limited, non-exclusive license to use the software during your subscription period",
        "You retain ownership of your business data entered into the system",
        "We may use aggregated, anonymized data for service improvement and analytics",
        "Any feedback or suggestions you provide may be used by us without compensation"
      ]
    },
    {
      title: "Data and Privacy",
      content: [
        "We collect and process your data in accordance with our Privacy Policy and Cookie Policy",
        "You are responsible for ensuring you have the right to provide any data to us",
        "We implement industry-standard security measures to protect your data",
        "You may export your data at any time during your subscription period",
        "Data retention policies are outlined in our Privacy Policy",
        "Cookie usage and preferences are governed by our Cookie Policy"
      ]
    },
    {
      title: "Privacy and Data Protection Compliance",
      content: [
        "We comply with applicable data protection laws including GDPR and Indian data protection regulations",
        "Your personal data is processed in accordance with our Privacy Policy and Cookie Policy",
        "You have specific rights regarding your personal data and cookie preferences under applicable laws",
        "We implement appropriate technical and organizational measures to protect your data",
        "Cross-border data transfers are conducted with adequate safeguards in place",
        "You may exercise your data protection rights by contacting <NAME_EMAIL>"
      ]
    },
    {
      title: "Limitation of Liability",
      content: [
        "Our liability is limited to the maximum extent permitted by law",
        "We are not liable for indirect, incidental, or consequential damages",
        "Our total liability shall not exceed the amount paid by you in the 12 months preceding the claim",
        "Some jurisdictions do not allow limitation of liability, so these limitations may not apply to you",
        "Liability limitations do not apply to data protection violations or privacy breaches where prohibited by law"
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      {/* Hero Section */}
      <section className="w-full bg-gradient-hero py-16 lg:py-24">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="text-center max-w-4xl mx-auto space-y-8">
            <Badge variant="secondary" className="text-sm font-medium bg-gray-100 text-gray-700 rounded-full px-4 py-2">
              Legal Agreement
            </Badge>
            
            <h1 className="text-4xl lg:text-6xl font-bold text-foreground leading-tight">
              Terms of Service
            </h1>
            
            <p className="text-lg text-muted-foreground leading-relaxed max-w-3xl mx-auto">
              These Terms of Service govern your use of ERIA POS software and services 
              provided by Eria Software Solutions & Services Private Limited.
            </p>

            <div className="text-sm text-muted-foreground">
              Last updated: January 2025
            </div>
          </div>
        </div>
      </section>

      {/* Introduction */}
      <section className="w-full py-16 lg:py-24 bg-background">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <Card className="border-0 shadow-soft bg-muted/20">
              <CardContent className="p-8">
                <h2 className="text-2xl font-bold text-foreground mb-4">
                  Agreement Overview
                </h2>
                <p className="text-muted-foreground leading-relaxed">
                  This Terms of Service agreement ("Agreement") is a legal contract between you and 
                  Eria Software Solutions & Services Private Limited ("Company," "we," "us," or "our") 
                  regarding your use of our ERIA POS software, website, and related services 
                  (collectively, the "Service"). Please read these terms carefully before using our services.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Terms Sections */}
      <section className="w-full py-16 lg:py-24 bg-muted/30">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="max-w-4xl mx-auto space-y-8">
            {sections.map((section, index) => (
              <Card key={index} className="border-0 shadow-soft bg-background">
                <CardContent className="p-8">
                  <h3 className="text-xl font-semibold text-foreground mb-4">
                    {section.title}
                  </h3>
                  <ul className="space-y-3">
                    {section.content.map((item, itemIndex) => (
                      <li key={itemIndex} className="flex items-start gap-3">
                        <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div>
                        <span className="text-muted-foreground leading-relaxed">{item}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Service Availability */}
      <section className="w-full py-16 lg:py-24 bg-background">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <Card className="border-0 shadow-soft bg-background">
              <CardContent className="p-8">
                <h3 className="text-xl font-semibold text-foreground mb-4">
                  Service Availability and Support
                </h3>
                <p className="text-muted-foreground leading-relaxed mb-4">
                  We strive to provide reliable service with minimal downtime. However, we cannot 
                  guarantee uninterrupted service availability.
                </p>
                <ul className="space-y-2">
                  <li className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div>
                    <span className="text-muted-foreground">We target 99.9% uptime for our services</span>
                  </li>
                  <li className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div>
                    <span className="text-muted-foreground">Scheduled maintenance will be announced in advance</span>
                  </li>
                  <li className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div>
                    <span className="text-muted-foreground">Technical support is available during business hours</span>
                  </li>
                  <li className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div>
                    <span className="text-muted-foreground">Emergency support is available for critical issues</span>
                  </li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Termination */}
      <section className="w-full py-16 lg:py-24 bg-muted/20">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <Card className="border-0 shadow-soft bg-background">
              <CardContent className="p-8">
                <h3 className="text-xl font-semibold text-foreground mb-4">
                  Termination
                </h3>
                <p className="text-muted-foreground leading-relaxed mb-4">
                  Either party may terminate this agreement under certain conditions:
                </p>
                <ul className="space-y-2 mb-6">
                  <li className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div>
                    <span className="text-muted-foreground">You may cancel your subscription at any time with 30 days' notice</span>
                  </li>
                  <li className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div>
                    <span className="text-muted-foreground">We may terminate for breach of terms or non-payment</span>
                  </li>
                  <li className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div>
                    <span className="text-muted-foreground">Upon termination, you will have 30 days to export your data</span>
                  </li>
                  <li className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div>
                    <span className="text-muted-foreground">All rights and licenses granted to you will cease upon termination</span>
                  </li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Limitation of Liability */}
      <section className="w-full py-16 lg:py-24 bg-background">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <Card className="border-0 shadow-soft bg-yellow-50">
              <CardContent className="p-8">
                <h3 className="text-xl font-semibold text-foreground mb-4">
                  Limitation of Liability
                </h3>
                <p className="text-muted-foreground leading-relaxed mb-4">
                  To the maximum extent permitted by law, Eria Software Solutions & Services Pvt Ltd 
                  shall not be liable for any indirect, incidental, special, consequential, or punitive 
                  damages, including but not limited to loss of profits, data, or business interruption.
                </p>
                <p className="text-muted-foreground leading-relaxed">
                  Our total liability for any claims arising from or related to this agreement shall not 
                  exceed the amount paid by you for the service during the twelve (12) months preceding 
                  the claim.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Contact Information */}
      <section className="w-full py-16 lg:py-24 bg-muted/20">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <Card className="border-0 shadow-soft bg-background">
              <CardContent className="p-8">
                <h3 className="text-xl font-semibold text-foreground mb-4">
                  Contact Information
                </h3>
                <p className="text-muted-foreground leading-relaxed mb-6">
                  If you have any questions about these Terms of Service, please contact us:
                </p>
                
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium text-foreground mb-1">Company:</h4>
                    <p className="text-muted-foreground">Eria Software Solutions & Services Private Limited</p>
                  </div>
                  
                  <div>
                    <h4 className="font-medium text-foreground mb-1">Email:</h4>
                    <p className="text-muted-foreground">
                      <a href="mailto:<EMAIL>" className="text-blue-600 hover:text-blue-700">
                        <EMAIL>
                      </a>
                    </p>
                  </div>
                  
                  <div>
                    <h4 className="font-medium text-foreground mb-1">Phone:</h4>
                    <p className="text-muted-foreground">
                      <a href="tel:+************" className="text-blue-600 hover:text-blue-700">
                        +91 9604069989
                      </a>
                    </p>
                  </div>
                  
                  <div>
                    <h4 className="font-medium text-foreground mb-1">Address:</h4>
                    <p className="text-muted-foreground">Mumbai, Maharashtra, India</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default withPageLoader(TermsOfService, {
  loadingText: "Loading Terms of Service...",
  minLoadingTime: 600
});
