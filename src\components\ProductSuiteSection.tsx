import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";

const ProductSuiteSection = () => {
  const products = [
    {
      id: "website-development",
      category: "वेबसाइट डेव्हलपमेंट",
      title: "व्यावसायिक रेस्टॉरंट वेबसाइट्स",
      description: "आपल्या रेस्टॉरंटचा ब्रँड दाखवणार्‍या आणि ऑनलाइन ऑर्डर वाढवणार्‍या आश्चर्यकारक, रिस्पॉन्सिव्ह वेबसाइट्स तयार करा. आमच्या कस्टम वेब डेव्हलपमेंट सोल्यूशन्समध्ये ऑनलाइन मेनू डिस्प्ले, आरक्षण प्रणाली, ग्राहक पुनरावलोकन एकीकरण आणि मोबाइल-ऑप्टिमाइझ्ड डिझाइन समाविष्ट आहेत जे पाहुण्यांना ग्राहकांमध्ये रूपांतरित करतात.",
      features: [
        "मोबाइल-रिस्पॉन्सिव्ह डिझाइन",
        "ऑनलाइन मेनू एकीकरण",
        "आरक्षण प्रणाली"
      ],
      image: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=600&h=400&fit=crop"
    },
    {
      id: "app-development",
      category: "अॅप डेव्हलपमेंट",
      title: "कस्टम मोबाइल अॅप्लिकेशन्स",
      description: "निर्बाध ऑर्डरिंग, लॉयल्टी प्रोग्राम आणि पुश नोटिफिकेशन ऑफर करणार्‍या नेटिव्ह मोबाइल अॅप्ससह ग्राहकांना गुंतवून ठेवा. आमच्या अॅप डेव्हलपमेंट सेवा वापरकर्ता-अनुकूल इंटरफेस तयार करतात जे ग्राहक धारणा वाढवतात आणि वैयक्तिकृत अनुभवांद्वारे पुनरावृत्ती ऑर्डर वाढवतात.",
      features: [
        "नेटिव्ह iOS आणि Android अॅप्स",
        "पुश नोटिफिकेशन्स",
        "लॉयल्टी प्रोग्राम एकीकरण"
      ],
      image: "https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=600&h=400&fit=crop"
    },
    {
      id: "freshservice-crm",
      category: "फ्रेशसर्व्हिस CRM",
      title: "संपूर्ण ग्राहक संबंध व्यवस्थापन",
      description: "आमच्या व्यापक CRM सोल्यूशनसह आपल्या ग्राहक सेवा आणि समर्थन कामकाज सुव्यवस्थित करा. ग्राहक चौकशी व्यवस्थापित करा, सेवा विनंत्या ट्रॅक करा, फॉलो-अप स्वयंचलित करा आणि निष्ठा निर्माण करणारे अपवादात्मक सेवा अनुभव प्रदान करण्यासाठी तपशीलवार ग्राहक प्रोफाइल राखा.",
      features: [
        "ग्राहक सेवा स्वयंचलन",
        "सेवा विनंती ट्रॅकिंग",
        "ग्राहक प्रोफाइल व्यवस्थापन"
      ],
      image: "https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=600&h=400&fit=crop"
    }
  ];

  return (
    <section className="w-full py-16 lg:py-24 bg-background">
      <div className="container mx-auto px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="text-sm font-semibold text-primary mb-4 uppercase tracking-wide">
            आमचा उत्पादन संच
          </div>
          <h2 className="text-3xl lg:text-5xl font-bold text-foreground mb-4">
            प्रत्येक व्यवसायासाठी यशाला चालना,<br />
            सर्व प्रकार, प्रत्येक आकार.
          </h2>
          
          {/* Product Navigation */}
          <div className="flex flex-wrap justify-center gap-4 mt-8">
            {products.map((product) => (
              <Badge key={product.id} variant="outline" className="px-4 py-2 cursor-pointer hover:bg-primary hover:text-primary-foreground transition-colors">
                {product.category}
              </Badge>
            ))}
          </div>
        </div>

        {/* Products */}
        <div className="space-y-24">
          {products.map((product, index) => (
            <div 
              key={product.id} 
              className={`grid lg:grid-cols-2 gap-12 items-center ${
                index % 2 === 1 ? 'lg:grid-flow-col-dense' : ''
              }`}
            >
              {/* Content */}
              <div className={`space-y-6 ${index % 2 === 1 ? 'lg:col-start-2' : ''}`}>
                <Badge variant="secondary" className="text-sm font-medium">
                  {product.category}
                </Badge>
                
                <h3 className="text-3xl lg:text-4xl font-bold text-foreground">
                  {product.title}
                </h3>
                
                <p className="text-lg text-muted-foreground leading-relaxed">
                  {product.description}
                </p>

                <ul className="space-y-2">
                  {product.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-center gap-2 text-foreground">
                      <div className="w-2 h-2 bg-primary rounded-full"></div>
                      {feature}
                    </li>
                  ))}
                </ul>

                <Button variant="link" className="p-0 font-semibold text-black hover:text-gray-700">
                  अधिक जाणून घ्या
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
              </div>

              {/* Image */}
              <div className={`${index % 2 === 1 ? 'lg:col-start-1 lg:row-start-1' : ''}`}>
                <Card className="overflow-hidden shadow-soft hover:shadow-lg transition-shadow">
                  <CardContent className="p-0">
                    <img 
                      src={product.image} 
                      alt={product.title}
                      className="w-full h-80 object-cover"
                    />
                  </CardContent>
                </Card>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ProductSuiteSection;