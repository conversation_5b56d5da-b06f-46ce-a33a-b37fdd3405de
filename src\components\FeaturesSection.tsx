import { Card, CardContent } from "@/components/ui/card";
import { 
  ShoppingCart, 
  Package, 
  Smartphone, 
  TrendingUp, 
  Users, 
  MessageCircle 
} from "lucide-react";

const FeaturesSection = () => {
  const features = [
    {
      icon: ShoppingCart,
      title: "प्रगत पॉइंट ऑफ सेल सिस्टम",
      description: "आमच्या क्लाउड-आधारित POS सह विजेसारख्या जलद ऑर्डर प्रक्रियेचा अनुभव घ्या जे जटिल ऑर्डर, स्प्लिट बिलिंग, टेबल व्यवस्थापन आणि मल्टी-पेमेंट पर्याय हाताळते. 99.9% अपटाइम विश्वसनीयता सुनिश्चित करताना व्यवहार वेळ 60% कमी करा."
    },
    {
      icon: Package,
      title: "स्मार्ट इन्व्हेंटरी नियंत्रण",
      description: "भविष्यसूचक विश्लेषण, स्वयंचलित पुनर्ऑर्डरिंग, रेसिपी कॉस्टिंग आणि पुरवठादार कार्यप्रदर्शन ट्रॅकिंगसह AI-संचालित इन्व्हेंटरी व्यवस्थापनासह स्टॉकआउट्स दूर करा आणि कचरा कमी करा. इन्व्हेंटरी खर्च 25% पर्यंत कमी करा."
    },
    {
      icon: Smartphone,
      title: "शून्य-कमिशन ऑनलाइन ऑर्डरिंग",
      description: "QR कोड मेनू, मोबाइल अॅप्स आणि वेबसाइट एकीकरणासह आमच्या एकीकृत ऑनलाइन ऑर्डरिंग प्लॅटफॉर्मसह महसूल वाढवा. तृतीय-पक्ष कमिशन फी काढून टाकताना सरासरी ऑर्डर मूल्य 27% वाढवा."
    },
    {
      icon: TrendingUp,
      title: "बिझनेस इंटेलिजन्स डॅशबोर्ड",
      description: "विक्री ट्रेंड, ग्राहक वर्तन, कर्मचारी कार्यप्रदर्शन आणि नफा मेट्रिक्स कव्हर करणार्या व्यापक विश्लेषणासह डेटा-चालित निर्णय घ्या. धोरणात्मक नियोजनासाठी रिअल-टाइम अंतर्दृष्टी आणि स्वयंचलित अहवाल मिळवा."
    },
    {
      icon: Users,
      title: "संपूर्ण कर्मचारी व्यवस्थापन",
      description: "प्रगत शेड्यूलिंग, वेळ ट्रॅकिंग, कार्यप्रदर्शन निरीक्षण आणि भूमिका-आधारित प्रवेश नियंत्रणांसह कर्मचारी ऑपरेशन्स सुव्यवस्थित करा. कर्मचारी उत्पादकता आणि जबाबदारी सुधारताना श्रम खर्च कमी करा."
    },
    {
      icon: MessageCircle,
      title: "WhatsApp बिझनेस एकीकरण",
      description: "स्वयंचलित ऑर्डर पुष्टीकरण, प्रचारात्मक मोहिमा, फीडबॅक संकलन आणि ग्राहक समर्थनासह WhatsApp द्वारे ग्राहकांशी थेट संवाद साधा. वैयक्तिकृत संवादाद्वारे ग्राहक धारणा 40% वाढवा."
    }
  ];

  return (
    <section className="w-full py-16 lg:py-24 bg-muted/30">
      <div className="container mx-auto px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl lg:text-4xl font-bold text-foreground mb-4">
            संपूर्ण रेस्टॉरंट व्यवस्थापन इकोसिस्टम
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Zyka POS च्या शक्तिशाली वैशिष्ट्यांचा एकीकृत संच आपल्या रेस्टॉरंट ऑपरेशन्सच्या प्रत्येक पैलूला कसे बदलतो ते शोधा,
            फ्रंट-ऑफ-हाउस सेवेपासून बॅक-ऑफिस व्यवस्थापनापर्यंत,
            मोजमापयोग्य परिणाम आणि शाश्वत वाढ प्रदान करते.
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <Card 
              key={index} 
              className="border-0 shadow-soft hover:shadow-lg transition-all duration-300 hover:scale-105 bg-background"
            >
              <CardContent className="p-8">
                <div className="space-y-4">
                  <div className="w-12 h-12 bg-gradient-primary rounded-lg flex items-center justify-center">
                    <feature.icon className="w-6 h-6 text-primary-foreground" />
                  </div>
                  
                  <h3 className="text-xl font-semibold text-foreground">
                    {feature.title}
                  </h3>
                  
                  <p className="text-muted-foreground leading-relaxed">
                    {feature.description}
                  </p>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default FeaturesSection;