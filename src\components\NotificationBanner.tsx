import { useState, useEffect } from "react";
import { X, Megaphone } from "lucide-react";

const NotificationBanner = () => {
  const [isVisible, setIsVisible] = useState(true);
  const [isDismissed, setIsDismissed] = useState(false);
  const [currentNotification, setCurrentNotification] = useState(0);

  const notifications = [
    "🎉 विशेष ऑफर: Zyka POS वर 30% सूट! आजच संपर्क करा!",
    "🚀 नवीन फीचर: WhatsApp एकीकरण आता उपलब्ध!",
    "⭐ 100+ रेस्टॉरंट्सचा विश्वास - आजच सामील व्हा!",
    "📞 मोफत डेमो बुक करा: +91 9604069989"
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentNotification((prev) => (prev + 1) % notifications.length);
    }, 4000);

    return () => clearInterval(interval);
  }, [notifications.length]);



  if (!isVisible || isDismissed) return null;

  return (
    <div className="fixed top-0 left-0 right-0 bg-gradient-to-r from-orange-500 to-red-500 text-white py-2 px-4 relative overflow-hidden z-50 transform transition-transform duration-300 ease-in-out">
      <div className="flex items-center justify-center space-x-2">
        <Megaphone className="w-4 h-4 animate-bounce" />
        <div className="text-sm font-medium text-center flex-1">
          <div className="animate-fade-in-out">
            {notifications[currentNotification]}
          </div>
        </div>
        <button
          onClick={() => setIsDismissed(true)}
          className="text-white hover:text-gray-200 transition-colors"
          aria-label="बंद करा"
        >
          <X className="w-4 h-4" />
        </button>
      </div>
      
      {/* Animated background elements */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-r from-transparent via-white to-transparent transform -skew-x-12 animate-shimmer"></div>
      </div>
    </div>
  );
};

export default NotificationBanner;
