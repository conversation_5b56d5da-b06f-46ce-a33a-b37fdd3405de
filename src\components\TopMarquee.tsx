import { Star, Zap, Shield, Clock } from "lucide-react";

const TopMarquee = () => {
  const highlights = [
    { icon: Star, text: "100+ आनंदी ग्राहक" },
    { icon: Zap, text: "99.9% अपटाइम हमी" },
    { icon: Shield, text: "24/7 समर्थन" },
    { icon: Clock, text: "30 सेकंदात सेटअप" },
    { icon: Star, text: "मोफत डेमो उपलब्ध" },
    { icon: Zap, text: "30% महसूल वाढ" },
    { icon: Shield, text: "सुरक्षित पेमेंट्स" },
    { icon: Clock, text: "जलद ऑर्डर प्रक्रिया" }
  ];

  return (
    <div className="bg-gradient-to-r from-green-500 via-blue-500 to-purple-500 py-2 overflow-hidden border-b border-gray-200">
      <div className="relative">
        {/* Marquee content */}
        <div className="flex animate-marquee-fast whitespace-nowrap">
          {highlights.concat(highlights).map((item, index) => (
            <div
              key={index}
              className="flex items-center mx-6 text-white"
            >
              <item.icon className="w-4 h-4 mr-2 animate-pulse" />
              <span className="text-sm font-medium">{item.text}</span>
              <span className="mx-4 text-white/60">•</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default TopMarquee;
