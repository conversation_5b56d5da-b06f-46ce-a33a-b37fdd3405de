import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Star } from "lucide-react";
import TypingAnimation from "./TypingAnimation";
import heroImage from "@/assets/restaurant-hero.jpg";
import posInterface from "@/assets/pos-interface.jpg";

const HeroSection = () => {
  const typingTexts = [
    "रेस्टॉरंट व्यवस्थापन",
    "ऑनलाइन ऑर्डरिंग",
    "इन्व्हेंटरी नियंत्रण",
    "ग्राहक सेवा"
  ];

  return (
    <section className="w-full bg-gradient-hero py-16 lg:py-24">
      <div className="container mx-auto px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Left Content */}
          <div className="space-y-8">
            <div className="space-y-4">
              <Badge variant="secondary" className="text-sm font-medium bg-gray-100 text-gray-700 rounded-full px-4 py-2">
                संपूर्ण रेस्टॉरंट व्यवस्थापन प्लॅटफॉर्म
              </Badge>

              <h1 className="text-4xl lg:text-6xl font-bold text-foreground leading-tight">
                Zyka POS सह आपल्या रेस्टॉरंटचे कामकाज क्रांतिकारक बनवा
              </h1>

              <p className="text-lg text-muted-foreground leading-relaxed max-w-lg">
                <TypingAnimation
                  texts={typingTexts}
                  className="text-blue-600 font-semibold"
                />
                <br />
                बुद्धिमान POS तंत्रज्ञानासह आपले रेस्टॉरंट बदला जे कामकाज सुव्यवस्थित करते
                आणि नफा वाढवते. जलद सेवा, स्मार्ट इन्व्हेंटरी व्यवस्थापन,
                आणि अपवादात्मक ग्राहक अनुभव मिळवा.
              </p>
            </div>

            <Button
              variant="hero"
              size="lg"
              className="text-lg px-8 py-6 bg-blue-600 text-white hover:bg-blue-700"
              onClick={() => window.open('https://zykapos.eriasoftware.com', '_blank')}
            >
              डेमो घ्या
            </Button>

            {/* Trust Indicators */}
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <Star className="w-5 h-5 text-primary fill-primary" />
                <span className="text-sm font-medium text-foreground">100+ रेस्टॉरंट्सचा विश्वास</span>
                <span className="text-sm text-muted-foreground">संपूर्ण भारतात</span>
              </div>
              <div className="flex items-center gap-2">
                <Star className="w-5 h-5 text-primary fill-primary" />
                <span className="text-sm font-medium text-foreground">100K+ ऑर्डर प्रक्रिया केल्या</span>
                <span className="text-sm text-muted-foreground">10+ शहरांमध्ये</span>
              </div>
            </div>
          </div>

          {/* Right Content - Images */}
          <div className="relative">
            <div className="relative">
              <img
                src={heroImage}
                alt="रेस्टॉरंट व्यावसायिक"
                className="w-full h-auto rounded-lg shadow-soft"
              />

              {/* POS Interface Overlay */}
              <div className="absolute -bottom-6 -left-6 lg:-left-12 w-3/4 max-w-sm">
                <img
                  src={posInterface}
                  alt="रेस्टॉरंट POS इंटरफेस"
                  className="w-full h-auto rounded-lg shadow-soft border-4 border-background"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;