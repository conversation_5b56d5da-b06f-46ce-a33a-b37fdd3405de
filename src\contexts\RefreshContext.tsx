import React, { createContext, useContext, useState, useCallback } from "react";

interface RefreshContextType {
  isRefreshing: boolean;
  refreshPage: () => void;
  refreshKey: number;
}

const RefreshContext = createContext<RefreshContextType | undefined>(undefined);

export const useRefresh = () => {
  const context = useContext(RefreshContext);
  if (!context) {
    throw new Error("useRefresh must be used within a RefreshProvider");
  }
  return context;
};

interface RefreshProviderProps {
  children: React.ReactNode;
}

export const RefreshProvider: React.FC<RefreshProviderProps> = ({ children }) => {
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [refreshKey, setRefreshKey] = useState(0);

  const refreshPage = useCallback(() => {
    setIsRefreshing(true);
    
    // Simulate refresh process
    setTimeout(() => {
      setRefreshKey(prev => prev + 1);
      setIsRefreshing(false);
    }, 1000);
  }, []);

  const value = {
    isRefreshing,
    refreshPage,
    refreshKey
  };

  return (
    <RefreshContext.Provider value={value}>
      {children}
    </RefreshContext.Provider>
  );
};
