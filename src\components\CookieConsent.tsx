import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>, Setting<PERSON>, Shield, Info } from "lucide-react";
import { Link } from "react-router-dom";
import { <PERSON>ieManager, type CookieConsent } from "@/lib/utils";
import CookiePreferenceCenter from "./CookiePreferenceCenter";

const CookieConsent = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [showPreferences, setShowPreferences] = useState(false);

  useEffect(() => {
    // Check if user has already given consent or needs consent update
    const hasConsent = CookieManager.hasConsent();
    const needsUpdate = CookieManager.needsConsentUpdate();

    if (!hasConsent || needsUpdate) {
      // Show banner after a short delay
      const timer = setTimeout(() => {
        setIsVisible(true);
      }, 1500);
      return () => clearTimeout(timer);
    }
  }, []);

  const handleAcceptAll = () => {
    const consent: Partial<CookieConsent> = {
      essential: true,
      functional: true,
      analytics: true,
      marketing: true
    };
    CookieManager.setConsent(consent);
    setIsVisible(false);
  };

  const handleRejectAll = () => {
    const consent: Partial<CookieConsent> = {
      essential: true,
      functional: false,
      analytics: false,
      marketing: false
    };
    CookieManager.setConsent(consent);
    setIsVisible(false);
  };

  const handleCustomize = () => {
    setShowPreferences(true);
  };

  const handlePreferenceSave = (consent: Partial<CookieConsent>) => {
    CookieManager.setConsent(consent);
    setIsVisible(false);
    setShowPreferences(false);
  };

  const handleClose = () => {
    setIsVisible(false);
  };

  if (!isVisible) return null;

  return (
    <>
      <div className="fixed bottom-0 left-0 right-0 z-50 bg-white border-t border-gray-200 shadow-lg animate-in slide-in-from-bottom duration-300">
        <div className="container mx-auto px-4 py-4">
          <div className="flex flex-col lg:flex-row items-start lg:items-center justify-between gap-4">
            <div className="flex items-start gap-3 flex-1">
              <Shield className="w-6 h-6 text-blue-600 mt-0.5 flex-shrink-0" />
              <div className="text-sm text-gray-700">
                <p className="mb-2 font-semibold">
                  We respect your privacy and data protection rights
                </p>
                <p className="leading-relaxed">
                  We use cookies and similar technologies to provide essential website functionality,
                  analyze usage, and enhance your experience. Some cookies require your consent under
                  GDPR and Indian data protection laws.{" "}
                  <Link
                    to="/cookie-policy"
                    className="text-blue-600 hover:text-blue-700 underline font-medium"
                  >
                    Learn more about our cookies
                  </Link>
                  {" "}or{" "}
                  <Link
                    to="/privacy-policy"
                    className="text-blue-600 hover:text-blue-700 underline font-medium"
                  >
                    privacy policy
                  </Link>
                  .
                </p>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2 flex-shrink-0 w-full lg:w-auto">
              <Button
                onClick={handleCustomize}
                variant="outline"
                size="sm"
                className="text-sm px-4 py-2 border-gray-300 hover:bg-gray-50"
              >
                <Settings className="w-4 h-4 mr-2" />
                Customize
              </Button>
              <Button
                onClick={handleRejectAll}
                variant="outline"
                size="sm"
                className="text-sm px-4 py-2 border-gray-300 hover:bg-gray-50"
              >
                Reject All
              </Button>
              <Button
                onClick={handleAcceptAll}
                size="sm"
                className="text-sm px-4 py-2 bg-blue-600 text-white hover:bg-blue-700"
              >
                Accept All
              </Button>
              <Button
                onClick={handleClose}
                variant="ghost"
                size="sm"
                className="p-2 hover:bg-gray-100"
                title="Close banner (cookies will remain disabled)"
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
          </div>

          {/* Compliance Notice */}
          <div className="mt-3 pt-3 border-t border-gray-100">
            <div className="flex items-center gap-2 text-xs text-gray-500">
              <Info className="w-3 h-3" />
              <span>
                By clicking "Accept All", you consent to our use of cookies.
                Essential cookies cannot be disabled. You can change your preferences anytime.
              </span>
            </div>
          </div>
        </div>
      </div>

      <CookiePreferenceCenter
        isOpen={showPreferences}
        onClose={() => setShowPreferences(false)}
        onSave={handlePreferenceSave}
      />
    </>
  );
};

export default CookieConsent;
