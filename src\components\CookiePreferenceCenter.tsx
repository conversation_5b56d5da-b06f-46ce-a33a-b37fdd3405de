import React, { useState, useEffect } from 'react';
import { X, Shield, Settings, Info, Check, AlertCircle } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { <PERSON><PERSON>Manager, COOKIE_CATEGORIES, type CookieConsent } from '@/lib/utils';

interface CookiePreferenceCenterProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (consent: Partial<CookieConsent>) => void;
}

const CookiePreferenceCenter: React.FC<CookiePreferenceCenterProps> = ({
  isOpen,
  onClose,
  onSave
}) => {
  const [preferences, setPreferences] = useState({
    essential: true,
    functional: false,
    analytics: false,
    marketing: false
  });

  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    if (isOpen) {
      const consent = CookieManager.getConsent();
      if (consent) {
        setPreferences({
          essential: consent.essential,
          functional: consent.functional,
          analytics: consent.analytics,
          marketing: consent.marketing
        });
      }
    }
  }, [isOpen]);

  const handlePreferenceChange = (category: string, enabled: boolean) => {
    if (category === 'essential') return; // Essential cookies cannot be disabled
    
    setPreferences(prev => ({
      ...prev,
      [category]: enabled
    }));
  };

  const handleSavePreferences = () => {
    onSave(preferences);
    onClose();
  };

  const handleAcceptAll = () => {
    const allEnabled = {
      essential: true,
      functional: true,
      analytics: true,
      marketing: true
    };
    setPreferences(allEnabled);
    onSave(allEnabled);
    onClose();
  };

  const handleRejectAll = () => {
    const essentialOnly = {
      essential: true,
      functional: false,
      analytics: false,
      marketing: false
    };
    setPreferences(essentialOnly);
    onSave(essentialOnly);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 bg-black/50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center gap-3">
            <Shield className="w-6 h-6 text-blue-600" />
            <h2 className="text-xl font-semibold">Cookie Preferences</h2>
          </div>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="w-5 h-5" />
          </Button>
        </div>

        <div className="flex h-[calc(90vh-140px)]">
          {/* Sidebar */}
          <div className="w-64 border-r bg-gray-50 p-4">
            <nav className="space-y-2">
              <button
                onClick={() => setActiveTab('overview')}
                className={`w-full text-left px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                  activeTab === 'overview' 
                    ? 'bg-blue-100 text-blue-700' 
                    : 'text-gray-600 hover:bg-gray-100'
                }`}
              >
                <Info className="w-4 h-4 inline mr-2" />
                Overview
              </button>
              {COOKIE_CATEGORIES.map((category) => (
                <button
                  key={category.id}
                  onClick={() => setActiveTab(category.id)}
                  className={`w-full text-left px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                    activeTab === category.id 
                      ? 'bg-blue-100 text-blue-700' 
                      : 'text-gray-600 hover:bg-gray-100'
                  }`}
                >
                  {category.name}
                  {preferences[category.id as keyof typeof preferences] && (
                    <Check className="w-4 h-4 inline ml-2 text-green-600" />
                  )}
                </button>
              ))}
            </nav>
          </div>

          {/* Content */}
          <div className="flex-1 p-6 overflow-y-auto">
            {activeTab === 'overview' ? (
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold mb-2">About Cookies</h3>
                  <p className="text-gray-600 leading-relaxed">
                    We use cookies and similar technologies to provide, protect, and improve our services. 
                    This preference center allows you to control which cookies we can use. You can change 
                    your preferences at any time.
                  </p>
                </div>

                <div className="bg-blue-50 p-4 rounded-lg">
                  <div className="flex items-start gap-3">
                    <AlertCircle className="w-5 h-5 text-blue-600 mt-0.5" />
                    <div>
                      <h4 className="font-medium text-blue-900 mb-1">Your Rights</h4>
                      <p className="text-sm text-blue-800">
                        Under GDPR and Indian data protection laws, you have the right to access, 
                        correct, delete, or port your data. You can also withdraw consent at any time.
                      </p>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  {COOKIE_CATEGORIES.map((category) => (
                    <Card key={category.id} className="border">
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2">
                            <h4 className="font-medium">{category.name}</h4>
                            <Badge variant={category.required ? "default" : "outline"}>
                              {category.required ? "Required" : "Optional"}
                            </Badge>
                          </div>
                          <Switch
                            checked={preferences[category.id as keyof typeof preferences]}
                            onCheckedChange={(checked) => handlePreferenceChange(category.id, checked)}
                            disabled={category.required}
                          />
                        </div>
                        <p className="text-sm text-gray-600">{category.description}</p>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            ) : (
              <div className="space-y-6">
                {COOKIE_CATEGORIES.filter(cat => cat.id === activeTab).map((category) => (
                  <div key={category.id}>
                    <div className="flex items-center justify-between mb-4">
                      <div>
                        <h3 className="text-lg font-semibold">{category.name}</h3>
                        <p className="text-gray-600 mt-1">{category.description}</p>
                      </div>
                      <Switch
                        checked={preferences[category.id as keyof typeof preferences]}
                        onCheckedChange={(checked) => handlePreferenceChange(category.id, checked)}
                        disabled={category.required}
                      />
                    </div>

                    <div className="space-y-4">
                      <h4 className="font-medium">Cookies in this category:</h4>
                      {category.cookies.map((cookie, index) => (
                        <Card key={index} className="border">
                          <CardContent className="p-4">
                            <div className="grid grid-cols-2 gap-4">
                              <div>
                                <h5 className="font-medium mb-1">{cookie.name}</h5>
                                <p className="text-sm text-gray-600">{cookie.purpose}</p>
                              </div>
                              <div className="space-y-2 text-sm">
                                <div><strong>Duration:</strong> {cookie.duration}</div>
                                <div><strong>Type:</strong> {cookie.type}</div>
                                <div><strong>Provider:</strong> {cookie.provider}</div>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="border-t p-6">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-500">
              Last updated: January 2025 | Version 1.0
            </div>
            <div className="flex gap-3">
              <Button variant="outline" onClick={handleRejectAll}>
                Reject All
              </Button>
              <Button variant="outline" onClick={handleAcceptAll}>
                Accept All
              </Button>
              <Button onClick={handleSavePreferences} className="bg-blue-600 text-white hover:bg-blue-700">
                Save Preferences
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CookiePreferenceCenter;
