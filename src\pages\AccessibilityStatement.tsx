import { useEffect } from "react";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { updatePageSEO } from "@/lib/seo";
import withPageLoader from "@/components/withPageLoader";
import { 
  Eye, 
  Keyboard, 
  MousePointer, 
  Volume2, 
  Monitor, 
  Smartphone,
  Mail,
  Phone,
  CheckCircle,
  AlertCircle
} from "lucide-react";

const AccessibilityStatement = () => {
  useEffect(() => {
    updatePageSEO('/accessibility-statement');
  }, []);

  const accessibilityFeatures = [
    {
      icon: Eye,
      title: "Visual Accessibility",
      features: [
        "High contrast color schemes",
        "Scalable text and UI elements",
        "Alternative text for images",
        "Clear visual hierarchy"
      ]
    },
    {
      icon: Keyboard,
      title: "Keyboard Navigation",
      features: [
        "Full keyboard navigation support",
        "Logical tab order",
        "Visible focus indicators",
        "Keyboard shortcuts"
      ]
    },
    {
      icon: Volume2,
      title: "Screen Reader Support",
      features: [
        "Semantic HTML structure",
        "ARIA labels and descriptions",
        "Screen reader compatible forms",
        "Descriptive link text"
      ]
    },
    {
      icon: Monitor,
      title: "Responsive Design",
      features: [
        "Mobile-friendly interface",
        "Flexible layouts",
        "Touch-friendly controls",
        "Zoom support up to 200%"
      ]
    }
  ];

  const complianceStandards = [
    {
      standard: "WCAG 2.1 Level AA",
      status: "Compliant",
      description: "Web Content Accessibility Guidelines compliance for international accessibility standards"
    },
    {
      standard: "Section 508",
      status: "Compliant",
      description: "US federal accessibility requirements for electronic and information technology"
    },
    {
      standard: "EN 301 549",
      status: "Compliant",
      description: "European accessibility standard for ICT products and services"
    },
    {
      standard: "Indian Guidelines",
      status: "Compliant",
      description: "Guidelines for Indian Government Websites (GIGW) accessibility requirements"
    }
  ];

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      {/* Hero Section */}
      <section className="w-full bg-gradient-hero py-16 lg:py-24">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="text-center max-w-4xl mx-auto space-y-8">
            <Badge variant="secondary" className="text-sm font-medium bg-gray-100 text-gray-700 rounded-full px-4 py-2">
              <Eye className="w-4 h-4 mr-2" />
              Accessibility Commitment
            </Badge>
            
            <h1 className="text-4xl lg:text-6xl font-bold text-foreground leading-tight">
              Accessibility Statement
            </h1>
            
            <p className="text-lg text-muted-foreground leading-relaxed max-w-3xl mx-auto">
              Eria Software is committed to ensuring digital accessibility for all users, including those with disabilities. 
              We strive to provide an inclusive experience that meets international accessibility standards.
            </p>

            <div className="text-sm text-muted-foreground">
              Last updated: January 2025 | WCAG 2.1 Level AA Compliant
            </div>
          </div>
        </div>
      </section>

      {/* Our Commitment */}
      <section className="w-full py-16 lg:py-24 bg-background">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <Card className="border-0 shadow-soft bg-blue-50">
              <CardContent className="p-8">
                <div className="flex items-start gap-4">
                  <CheckCircle className="w-8 h-8 text-blue-600 mt-1" />
                  <div>
                    <h2 className="text-2xl font-bold text-blue-900 mb-4">
                      Our Accessibility Commitment
                    </h2>
                    <p className="text-blue-800 leading-relaxed mb-4">
                      We believe that everyone should have equal access to information and functionality on the web. 
                      Our commitment to accessibility is ongoing, and we continuously work to improve the user experience 
                      for all visitors to our website.
                    </p>
                    <p className="text-blue-800 leading-relaxed">
                      This accessibility statement applies to the Eria Software website (eriasoftware.com) and 
                      reflects our efforts to ensure compliance with accessibility standards and guidelines.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Accessibility Features */}
      <section className="w-full py-16 lg:py-24 bg-muted/30">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-foreground mb-4">
              Accessibility Features
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Our website includes various features designed to improve accessibility and usability for all users.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            {accessibilityFeatures.map((feature, index) => (
              <Card key={index} className="border-0 shadow-soft bg-background">
                <CardContent className="p-6">
                  <div className="flex items-start gap-4">
                    <div className="bg-blue-100 p-3 rounded-lg">
                      <feature.icon className="w-6 h-6 text-blue-600" />
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold text-foreground mb-3">
                        {feature.title}
                      </h3>
                      <ul className="space-y-2">
                        {feature.features.map((item, itemIndex) => (
                          <li key={itemIndex} className="flex items-start gap-2">
                            <CheckCircle className="w-4 h-4 text-green-600 mt-0.5 flex-shrink-0" />
                            <span className="text-muted-foreground text-sm">{item}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Compliance Standards */}
      <section className="w-full py-16 lg:py-24 bg-background">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-foreground mb-4">
              Compliance Standards
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              We adhere to internationally recognized accessibility standards and guidelines.
            </p>
          </div>

          <div className="max-w-4xl mx-auto space-y-6">
            {complianceStandards.map((standard, index) => (
              <Card key={index} className="border-0 shadow-soft bg-background">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-lg font-semibold text-foreground mb-2">
                        {standard.standard}
                      </h3>
                      <p className="text-muted-foreground text-sm">
                        {standard.description}
                      </p>
                    </div>
                    <Badge 
                      variant="outline" 
                      className="bg-green-50 text-green-700 border-green-200"
                    >
                      <CheckCircle className="w-4 h-4 mr-1" />
                      {standard.status}
                    </Badge>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Feedback and Contact */}
      <section className="w-full py-16 lg:py-24 bg-muted/20">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="max-w-2xl mx-auto text-center">
            <Card className="border-0 shadow-soft bg-background">
              <CardContent className="p-8">
                <AlertCircle className="w-12 h-12 text-blue-600 mx-auto mb-4" />
                <h2 className="text-2xl font-bold text-foreground mb-4">
                  Accessibility Feedback
                </h2>
                <p className="text-muted-foreground leading-relaxed mb-6">
                  We welcome your feedback on the accessibility of our website. If you encounter any 
                  accessibility barriers or have suggestions for improvement, please contact us.
                </p>
                
                <div className="space-y-4 mb-6">
                  <div className="flex items-center justify-center gap-2">
                    <Mail className="w-5 h-5 text-blue-600" />
                    <a 
                      href="mailto:<EMAIL>" 
                      className="text-blue-600 hover:text-blue-700 font-medium"
                    >
                      <EMAIL>
                    </a>
                  </div>
                  <div className="flex items-center justify-center gap-2">
                    <Phone className="w-5 h-5 text-blue-600" />
                    <a 
                      href="tel:+919604069989" 
                      className="text-blue-600 hover:text-blue-700 font-medium"
                    >
                      +91 9604069989
                    </a>
                  </div>
                </div>

                <p className="text-sm text-muted-foreground">
                  We aim to respond to accessibility feedback within 2 business days and will work 
                  to address any issues as quickly as possible.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default withPageLoader(AccessibilityStatement, {
  loadingText: "Loading Accessibility Statement...",
  minLoadingTime: 600
});
