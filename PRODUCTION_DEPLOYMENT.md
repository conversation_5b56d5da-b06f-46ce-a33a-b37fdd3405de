# Production Deployment Guide

## 🚀 reCAPTCHA Configuration for Production

Your reCAPTCHA keys have been configured for development. Here's how to deploy to production:

### 📋 Your reCAPTCHA Keys
- **Site Key (Public)**: `6Ld52YQrAAAAAJagT8vLUYW_iu23kbkS8_8zPyma`
- **Secret Key (Private)**: `6Ld52YQrAAAAAC8LG6Ol0GK3MoQOyrS0j6gMYMcf`

⚠️ **Security Note**: Only the site key is used in the frontend. Keep the secret key secure for backend verification.

## 🌐 Platform-Specific Deployment

### Vercel Deployment

1. **Deploy to Vercel**:
   ```bash
   # Install Vercel CLI (if not already installed)
   npm i -g vercel
   
   # Deploy
   vercel
   ```

2. **Add Environment Variables**:
   - Go to your Vercel project dashboard
   - Navigate to Settings → Environment Variables
   - Add the following:

   ```
   Name: VITE_RECAPTCHA_SITE_KEY
   Value: 6Ld52YQrAAAAAJagT8vLUYW_iu23kbkS8_8zPyma
   Environment: Production, Preview, Development
   ```

3. **Redeploy**:
   ```bash
   vercel --prod
   ```

### Netlify Deployment

1. **Deploy to Netlify**:
   ```bash
   # Build the project
   npm run build
   
   # Deploy dist folder to Netlify
   ```

2. **Add Environment Variables**:
   - Go to Site settings → Environment variables
   - Add:
   ```
   Key: VITE_RECAPTCHA_SITE_KEY
   Value: 6Ld52YQrAAAAAJagT8vLUYW_iu23kbkS8_8zPyma
   ```

3. **Trigger Redeploy**:
   - Go to Deploys → Trigger deploy

### GitHub Pages / Other Static Hosts

For static hosting, you'll need to build with environment variables:

```bash
# Set environment variable and build
VITE_RECAPTCHA_SITE_KEY=6Ld52YQrAAAAAJagT8vLUYW_iu23kbkS8_8zPyma npm run build
```

## 🔧 Domain Configuration

Make sure your production domain is added to your reCAPTCHA site settings:

1. **Go to Google reCAPTCHA Admin**: https://www.google.com/recaptcha/admin
2. **Select your site**
3. **Add your production domain**:
   - `eriasoftware.com`
   - `www.eriasoftware.com` (if using www)
   - Any other domains you'll use

## 🧪 Testing Production reCAPTCHA

After deployment:

1. **Visit your production site**
2. **Check contact form**: The reCAPTCHA should load without test messages
3. **Test form submission**: Fill out and submit the contact form
4. **Check demo booking**: Test the demo booking form
5. **Verify emails**: Ensure form submissions reach your email

## 🔒 Backend Verification (Optional)

For enhanced security, you can verify reCAPTCHA on your backend:

### Formspree Integration
Formspree automatically handles reCAPTCHA verification when the `g-recaptcha-response` field is included.

### Custom Backend Verification
If you have a custom backend:

```javascript
// Example Node.js verification
const verifyRecaptcha = async (token) => {
  const response = await fetch('https://www.google.com/recaptcha/api/siteverify', {
    method: 'POST',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    body: `secret=6Ld52YQrAAAAAC8LG6Ol0GK3MoQOyrS0j6gMYMcf&response=${token}`
  });
  
  const data = await response.json();
  return data.success;
};
```

## 📊 Monitoring

Monitor your reCAPTCHA usage:

1. **reCAPTCHA Admin Console**: https://www.google.com/recaptcha/admin
2. **Check analytics**: View solve rates and security reports
3. **Monitor form submissions**: Ensure legitimate users can submit forms

## ✅ Deployment Checklist

- [ ] Environment variables added to hosting platform
- [ ] Production domain added to reCAPTCHA settings
- [ ] Site deployed and accessible
- [ ] Contact form tested with reCAPTCHA
- [ ] Demo booking form tested
- [ ] Form submissions received via email
- [ ] No "testing purposes only" message visible
- [ ] reCAPTCHA analytics showing data

## 🆘 Troubleshooting

### Common Issues:

1. **"Invalid site key"**: 
   - Check environment variable is set correctly
   - Verify domain is added to reCAPTCHA settings

2. **reCAPTCHA not loading**:
   - Check browser console for errors
   - Verify network connectivity
   - Check Content Security Policy settings

3. **Forms not submitting**:
   - Verify Formspree endpoint is correct
   - Check browser network tab for failed requests
   - Ensure reCAPTCHA is completed before submission

### Support:
- **Technical Issues**: <EMAIL>
- **reCAPTCHA Help**: https://developers.google.com/recaptcha/docs/faq

Your reCAPTCHA is now configured and ready for production deployment! 🎉
