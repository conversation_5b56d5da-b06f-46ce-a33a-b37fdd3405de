import { Star, TrendingUp, Users, Award } from "lucide-react";

const MarqueeSection = () => {
  const achievements = [
    { icon: Users, text: "100+ आनंदी ग्राहक" },
    { icon: TrendingUp, text: "100K+ ऑर्डर प्रक्रिया केल्या" },
    { icon: Star, text: "4.9/5 ग्राहक रेटिंग" },
    { icon: Award, text: "10+ शहरांमध्ये सेवा" },
    { icon: Users, text: "99.9% अपटाइम हमी" },
    { icon: TrendingUp, text: "30% महसूल वाढ" },
    { icon: Star, text: "24/7 समर्थन" },
    { icon: Award, text: "ISO प्रमाणित" }
  ];

  return (
    <div className="bg-gradient-to-r from-blue-600 to-purple-600 py-4 overflow-hidden">
      <div className="relative">
        {/* First marquee - moving right to left */}
        <div className="flex animate-marquee whitespace-nowrap">
          {achievements.concat(achievements).map((item, index) => (
            <div
              key={index}
              className="flex items-center mx-8 text-white"
            >
              <item.icon className="w-5 h-5 mr-2" />
              <span className="text-lg font-medium">{item.text}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default MarqueeSection;
