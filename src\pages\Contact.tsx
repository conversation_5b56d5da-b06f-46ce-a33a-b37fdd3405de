import { useState, useRef, useEffect } from "react";
import ReCA<PERSON>TC<PERSON> from "react-google-recaptcha";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import SmallMarquee from "@/components/SmallMarquee";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { useToast } from "@/hooks/use-toast";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import withPageLoader from "@/components/withPageLoader";
import { Link } from "react-router-dom";
import { FormSecurity, InputValidator } from "@/lib/security";
import { updatePageSEO } from "@/lib/seo";
import {
  Mail,
  Phone,
  MapPin,
  Clock,
  MessageCircle,
  Send,
  CheckCircle,
  Users,
  Globe,
  Shield,
  AlertCircle
} from "lucide-react";

const Contact = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [captchaValue, setCaptchaValue] = useState<string | null>(null);
  const [dataProcessingConsent, setDataProcessingConsent] = useState(false);
  const [marketingConsent, setMarketingConsent] = useState(false);
  const [formErrors, setFormErrors] = useState<string[]>([]);
  const recaptchaRef = useRef<ReCAPTCHA>(null);
  const { toast } = useToast();

  useEffect(() => {
    updatePageSEO('/contact');
  }, []);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setFormErrors([]);

    // Rate limiting check
    if (!InputValidator.checkRateLimit('contact_form', 3, 300000)) {
      toast({
        title: "Too many attempts",
        description: "Please wait 5 minutes before submitting another form.",
        variant: "destructive",
      });
      return;
    }

    if (!captchaValue) {
      toast({
        title: "CAPTCHA verification required",
        description: "Please complete the CAPTCHA verification to submit the form.",
        variant: "destructive",
      });
      return;
    }

    if (!dataProcessingConsent) {
      toast({
        title: "Consent required",
        description: "Please consent to data processing to submit the form.",
        variant: "destructive",
      });
      return;
    }

    const formData = new FormData(e.currentTarget);
    const formObject = {
      firstName: formData.get('firstName') as string,
      lastName: formData.get('lastName') as string,
      email: formData.get('email') as string,
      phone: formData.get('phone') as string,
      subject: formData.get('subject') as string,
      message: formData.get('message') as string
    };

    // Validate form data
    const validation = FormSecurity.validateContactForm(formObject);
    if (!validation.isValid) {
      setFormErrors(validation.errors);
      toast({
        title: "Form validation failed",
        description: "Please correct the errors and try again.",
        variant: "destructive",
      });
      return;
    }

    // Check for suspicious activity
    if (FormSecurity.detectSuspiciousActivity(formObject)) {
      toast({
        title: "Security check failed",
        description: "Your submission contains suspicious content. Please contact us directly.",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      const formData = new FormData(e.currentTarget);
      formData.append("g-recaptcha-response", captchaValue);
      formData.append("dataProcessingConsent", dataProcessingConsent.toString());
      formData.append("marketingConsent", marketingConsent.toString());

      const response = await fetch("https://formspree.io/f/mjkrvyna", {
        method: "POST",
        body: formData,
        headers: {
          Accept: "application/json",
        },
      });

      if (response.ok) {
        toast({
          title: "Message sent successfully!",
          description: "Thank you for contacting us. We'll get back to you within 24 hours.",
        });
        (e.target as HTMLFormElement).reset();
        setCaptchaValue(null);
        setDataProcessingConsent(false);
        setMarketingConsent(false);
        recaptchaRef.current?.reset();
      } else {
        throw new Error("Failed to send message");
      }
    } catch (error) {
      toast({
        title: "Failed to send message",
        description: "Please try again or contact us <NAME_EMAIL>",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const contactMethods = [
    {
      icon: Mail,
      title: "ईमेल समर्थन",
      description: "तांत्रिक समस्या आणि सामान्य चौकशीसाठी मदत मिळवा",
      contact: "<EMAIL>",
      link: "mailto:<EMAIL>"
    },
    {
      icon: Phone,
      title: "फोन समर्थन",
      description: "आमच्या समर्थन टीमशी थेट बोला",
      contact: "+91 9604069989",
      link: "tel:+919604069989"
    },
    {
      icon: Mail,
      title: "सामान्य चौकशी",
      description: "विक्री, भागीदारी आणि व्यवसाय चौकशीसाठी",
      contact: "<EMAIL>",
      link: "mailto:<EMAIL>"
    }
  ];

  const officeInfo = [
    {
      icon: MapPin,
      title: "स्थान",
      info: "मुंबई, महाराष्ट्र, भारत"
    },
    {
      icon: Clock,
      title: "व्यवसाय वेळा",
      info: "सोमवार - शनिवार: सकाळी 9:00 - संध्याकाळी 6:00"
    },
    {
      icon: Globe,
      title: "सेवा क्षेत्रे",
      info: "संपूर्ण भारतातील व्यवसायांना सेवा देत आहोत"
    }
  ];

  return (
    <div className="min-h-screen bg-background">
      <Header />
      <SmallMarquee />
      
      {/* Hero Section */}
      <section className="w-full bg-gradient-hero py-16 lg:py-24">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="text-center max-w-4xl mx-auto space-y-8">
            <Badge variant="secondary" className="text-sm font-medium bg-gray-100 text-gray-700 rounded-full px-4 py-2">
              संपर्कात रहा
            </Badge>

            <h1 className="text-4xl lg:text-6xl font-bold text-foreground leading-tight">
              रिया सॉफ्टवेअरशी संपर्क करा
            </h1>

            <p className="text-lg text-muted-foreground leading-relaxed max-w-3xl mx-auto">
              आमच्या तंत्रज्ञान समाधानांसह आपला व्यवसाय बदलण्यास तयार आहात? वैयक्तिकृत समर्थन, डेमो आणि सल्लामसलतसाठी
              आमच्या टीमशी संपर्क साधा.
            </p>
          </div>
        </div>
      </section>

      {/* Contact Methods */}
      <section className="w-full py-16 lg:py-24 bg-muted/30">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-foreground mb-4">
              Multiple Ways to Reach Us
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Choose the most convenient way to get in touch with our team.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {contactMethods.map((method, index) => (
              <Card 
                key={index} 
                className="border-0 shadow-soft hover:shadow-lg transition-all duration-300 hover:scale-105 bg-background text-center"
              >
                <CardContent className="p-8">
                  <div className="space-y-4">
                    <div className="w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center mx-auto">
                      <method.icon className="w-8 h-8 text-primary-foreground" />
                    </div>
                    
                    <h3 className="text-xl font-semibold text-foreground">
                      {method.title}
                    </h3>
                    
                    <p className="text-muted-foreground leading-relaxed">
                      {method.description}
                    </p>

                    <a 
                      href={method.link}
                      className="inline-block text-blue-600 font-medium hover:text-blue-700 transition-colors"
                    >
                      {method.contact}
                    </a>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Office Information */}
      <section className="w-full py-16 lg:py-24 bg-background">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-foreground mb-4">
              Office Information
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Learn more about our location and business hours.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            {officeInfo.map((info, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <info.icon className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-foreground mb-2">
                  {info.title}
                </h3>
                <p className="text-muted-foreground">
                  {info.info}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Form */}
      <section className="w-full py-16 lg:py-24 bg-muted/20">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="max-w-2xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl lg:text-4xl font-bold text-foreground mb-4">
                Send Us a Message
              </h2>
              <p className="text-lg text-muted-foreground">
                Fill out the form below and we'll get back to you within 24 hours.
              </p>
            </div>

            <Card className="border-0 shadow-soft bg-background">
              <CardContent className="p-8">
                {formErrors.length > 0 && (
                  <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                    <div className="flex items-start gap-2">
                      <AlertCircle className="w-5 h-5 text-red-600 mt-0.5 flex-shrink-0" />
                      <div>
                        <h4 className="font-medium text-red-800 mb-2">Please correct the following errors:</h4>
                        <ul className="text-sm text-red-700 space-y-1">
                          {formErrors.map((error, index) => (
                            <li key={index}>• {error}</li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </div>
                )}

                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2">
                        First Name *
                      </label>
                      <input
                        type="text"
                        name="firstName"
                        required
                        className="w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="Enter your first name"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2">
                        Last Name *
                      </label>
                      <input
                        type="text"
                        name="lastName"
                        required
                        className="w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="Enter your last name"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-foreground mb-2">
                      Email Address *
                    </label>
                    <input
                      type="email"
                      name="email"
                      required
                      className="w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Enter your email address"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-foreground mb-2">
                      Phone Number
                    </label>
                    <input
                      type="tel"
                      name="phone"
                      className="w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Enter your phone number"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-foreground mb-2">
                      Subject *
                    </label>
                    <select
                      name="subject"
                      required
                      className="w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="">Select a subject</option>
                      <option value="demo">Request Demo</option>
                      <option value="support">Technical Support</option>
                      <option value="sales">Sales Inquiry</option>
                      <option value="partnership">Partnership</option>
                      <option value="other">Other</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-foreground mb-2">
                      Message *
                    </label>
                    <textarea
                      name="message"
                      required
                      rows={5}
                      className="w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Tell us about your requirements..."
                    ></textarea>
                  </div>

                  {/* Consent Checkboxes */}
                  <div className="space-y-3 p-4 bg-gray-50 rounded-lg border">
                    <div className="space-y-3">
                      <div className="flex items-start space-x-3">
                        <Checkbox
                          id="dataProcessingConsent"
                          checked={dataProcessingConsent}
                          onCheckedChange={(checked) => setDataProcessingConsent(checked as boolean)}
                          className="mt-0.5"
                        />
                        <label htmlFor="dataProcessingConsent" className="text-sm text-gray-700 cursor-pointer">
                          <span className="text-red-600">*</span> I agree to the processing of my data to respond to this inquiry.
                          See our{" "}
                          <Link to="/privacy-policy" className="text-blue-600 hover:underline">
                            Privacy Policy
                          </Link>
                          .
                        </label>
                      </div>

                      <div className="flex items-start space-x-3">
                        <Checkbox
                          id="marketingConsent"
                          checked={marketingConsent}
                          onCheckedChange={(checked) => setMarketingConsent(checked as boolean)}
                          className="mt-0.5"
                        />
                        <label htmlFor="marketingConsent" className="text-sm text-gray-700 cursor-pointer">
                          I'd like to receive product updates and offers (optional).
                        </label>
                      </div>
                    </div>

                    <div className="text-xs text-gray-600 bg-gray-100 p-2 rounded">
                      <Shield className="w-3 h-3 inline mr-1" />
                      You can withdraw consent anytime at{" "}
                      <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline">
                        <EMAIL>
                      </a>
                    </div>
                  </div>

                  {/* reCAPTCHA */}
                  <div className="flex justify-center">
                    <ReCAPTCHA
                      ref={recaptchaRef}
                      sitekey={import.meta.env.VITE_RECAPTCHA_SITE_KEY || "6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI"}
                      onChange={(value) => setCaptchaValue(value)}
                      onExpired={() => setCaptchaValue(null)}
                    />
                  </div>

                  <Button
                    type="submit"
                    variant="hero"
                    size="lg"
                    disabled={isSubmitting}
                    className="w-full text-lg px-8 py-6 bg-blue-600 text-white hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isSubmitting ? (
                      <>
                        <LoadingSpinner size="sm" className="mr-2" />
                        Sending...
                      </>
                    ) : (
                      <>
                        <Send className="w-5 h-5 mr-2" />
                        Send Message
                      </>
                    )}
                  </Button>
                </form>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="w-full py-16 lg:py-24 bg-black">
        <div className="container mx-auto px-6 lg:px-8 text-center">
          <div className="max-w-3xl mx-auto space-y-8">
            <h2 className="text-3xl lg:text-5xl font-bold text-white">
              Ready to Get Started?
            </h2>
            
            <p className="text-lg lg:text-xl text-white/90 leading-relaxed">
              Join 50+ businesses that trust Eria Software for their technology needs.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Button
                variant="hero"
                size="lg"
                className="text-lg px-8 py-6 bg-blue-600 text-white hover:bg-blue-700"
              >
                Schedule Demo
              </Button>

              <Button
                variant="outline"
                size="lg"
                className="text-lg px-8 py-6 bg-transparent border-2 border-white text-white hover:bg-white hover:text-black"
              >
                <Phone className="w-5 h-5 mr-2" />
                Call Now
              </Button>
            </div>

            <div className="pt-8 text-sm text-white/80">
              ✓ Free consultation • ✓ Quick response • ✓ Expert guidance • ✓ Custom solutions
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default withPageLoader(Contact, {
  loadingText: "Loading Contact Page...",
  minLoadingTime: 900
});
