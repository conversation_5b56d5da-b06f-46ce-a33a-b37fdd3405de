// Cookie Compliance Utilities for GDPR and Indian Data Protection Laws

export interface ComplianceReport {
  consentTimestamp: number;
  consentVersion: string;
  userAgent: string;
  ipAddress?: string;
  consentChoices: {
    essential: boolean;
    functional: boolean;
    analytics: boolean;
    marketing: boolean;
  };
  legalBasis: {
    essential: 'contractual_necessity';
    functional: 'legitimate_interest';
    analytics: 'consent';
    marketing: 'consent';
  };
  withdrawalHistory: Array<{
    timestamp: number;
    action: 'granted' | 'withdrawn';
    category: string;
  }>;
}

export class CookieComplianceManager {
  private static readonly COMPLIANCE_KEY = 'cookieCompliance';
  private static readonly AUDIT_LOG_KEY = 'cookieAuditLog';
  
  // Generate compliance report for auditing
  static generateComplianceReport(): ComplianceReport | null {
    try {
      const stored = localStorage.getItem(this.COMPLIANCE_KEY);
      if (!stored) return null;
      
      return JSON.parse(stored);
    } catch {
      return null;
    }
  }
  
  // Record consent action for audit trail
  static recordConsentAction(
    action: 'granted' | 'withdrawn',
    category: string,
    consent: any
  ): void {
    const timestamp = Date.now();
    const userAgent = navigator.userAgent;
    
    // Create compliance record
    const complianceRecord: ComplianceReport = {
      consentTimestamp: timestamp,
      consentVersion: '1.0',
      userAgent,
      consentChoices: {
        essential: consent.essential ?? true,
        functional: consent.functional ?? false,
        analytics: consent.analytics ?? false,
        marketing: consent.marketing ?? false
      },
      legalBasis: {
        essential: 'contractual_necessity',
        functional: 'legitimate_interest',
        analytics: 'consent',
        marketing: 'consent'
      },
      withdrawalHistory: this.getWithdrawalHistory()
    };
    
    // Add current action to history
    complianceRecord.withdrawalHistory.push({
      timestamp,
      action,
      category
    });
    
    // Store compliance record
    localStorage.setItem(this.COMPLIANCE_KEY, JSON.stringify(complianceRecord));
    
    // Update audit log
    this.updateAuditLog(action, category, timestamp);
  }
  
  private static getWithdrawalHistory(): Array<{
    timestamp: number;
    action: 'granted' | 'withdrawn';
    category: string;
  }> {
    try {
      const existing = localStorage.getItem(this.COMPLIANCE_KEY);
      if (existing) {
        const parsed = JSON.parse(existing);
        return parsed.withdrawalHistory || [];
      }
    } catch {
      // Ignore parsing errors
    }
    return [];
  }
  
  private static updateAuditLog(
    action: 'granted' | 'withdrawn',
    category: string,
    timestamp: number
  ): void {
    try {
      const existing = localStorage.getItem(this.AUDIT_LOG_KEY);
      const auditLog = existing ? JSON.parse(existing) : [];
      
      auditLog.push({
        timestamp,
        action,
        category,
        userAgent: navigator.userAgent,
        url: window.location.href
      });
      
      // Keep only last 100 entries
      if (auditLog.length > 100) {
        auditLog.splice(0, auditLog.length - 100);
      }
      
      localStorage.setItem(this.AUDIT_LOG_KEY, JSON.stringify(auditLog));
    } catch {
      // Ignore storage errors
    }
  }
  
  // Check if consent is still valid (not expired)
  static isConsentValid(): boolean {
    const report = this.generateComplianceReport();
    if (!report) return false;
    
    const consentAge = Date.now() - report.consentTimestamp;
    const maxAge = 365 * 24 * 60 * 60 * 1000; // 1 year in milliseconds
    
    return consentAge < maxAge;
  }
  
  // Get user's data protection rights information
  static getDataProtectionRights(): {
    gdpr: string[];
    indian: string[];
    contact: string;
  } {
    return {
      gdpr: [
        'Right to Access (Art. 15)',
        'Right to Rectification (Art. 16)',
        'Right to Erasure (Art. 17)',
        'Right to Restrict Processing (Art. 18)',
        'Right to Data Portability (Art. 20)',
        'Right to Object (Art. 21)',
        'Right to Withdraw Consent (Art. 7)'
      ],
      indian: [
        'Right to Confirmation and Access',
        'Right to Correction and Erasure',
        'Right to Data Portability',
        'Right to be Forgotten',
        'Right to Grievance Redressal'
      ],
      contact: '<EMAIL>'
    };
  }
  
  // Export user's cookie data for portability
  static exportCookieData(): string {
    const report = this.generateComplianceReport();
    const auditLog = localStorage.getItem(this.AUDIT_LOG_KEY);
    
    const exportData = {
      complianceReport: report,
      auditLog: auditLog ? JSON.parse(auditLog) : [],
      exportTimestamp: Date.now(),
      exportVersion: '1.0'
    };
    
    return JSON.stringify(exportData, null, 2);
  }
  
  // Clear all cookie data (for right to erasure)
  static clearAllCookieData(): void {
    localStorage.removeItem(this.COMPLIANCE_KEY);
    localStorage.removeItem(this.AUDIT_LOG_KEY);
    localStorage.removeItem('cookieConsent');
    
    // Clear all cookies
    document.cookie.split(";").forEach(cookie => {
      const eqPos = cookie.indexOf("=");
      const name = eqPos > -1 ? cookie.substr(0, eqPos) : cookie;
      document.cookie = `${name.trim()}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`;
    });
  }
  
  // Check if user is in EU (basic check)
  static isEUUser(): boolean {
    const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    const euTimezones = [
      'Europe/London', 'Europe/Berlin', 'Europe/Paris', 'Europe/Rome',
      'Europe/Madrid', 'Europe/Amsterdam', 'Europe/Brussels', 'Europe/Vienna',
      'Europe/Prague', 'Europe/Warsaw', 'Europe/Stockholm', 'Europe/Helsinki',
      'Europe/Dublin', 'Europe/Lisbon', 'Europe/Athens', 'Europe/Budapest'
    ];
    
    return euTimezones.some(tz => timezone.includes(tz));
  }
  
  // Check if user is in India
  static isIndianUser(): boolean {
    const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    return timezone.includes('Asia/Kolkata') || timezone.includes('Asia/Calcutta');
  }
  
  // Get applicable data protection laws
  static getApplicableLaws(): string[] {
    const laws = ['General Privacy Laws'];
    
    if (this.isEUUser()) {
      laws.push('GDPR (General Data Protection Regulation)');
    }
    
    if (this.isIndianUser()) {
      laws.push('Indian Personal Data Protection Act');
      laws.push('Information Technology Act, 2000');
    }
    
    return laws;
  }
  
  // Generate consent proof for legal compliance
  static generateConsentProof(): string {
    const report = this.generateComplianceReport();
    if (!report) return 'No consent record found';
    
    const laws = this.getApplicableLaws();
    
    return `
COOKIE CONSENT PROOF
====================
Timestamp: ${new Date(report.consentTimestamp).toISOString()}
Version: ${report.consentVersion}
User Agent: ${report.userAgent}
Applicable Laws: ${laws.join(', ')}

CONSENT CHOICES:
- Essential Cookies: ${report.consentChoices.essential ? 'GRANTED' : 'DENIED'} (Legal Basis: ${report.legalBasis.essential})
- Functional Cookies: ${report.consentChoices.functional ? 'GRANTED' : 'DENIED'} (Legal Basis: ${report.legalBasis.functional})
- Analytics Cookies: ${report.consentChoices.analytics ? 'GRANTED' : 'DENIED'} (Legal Basis: ${report.legalBasis.analytics})
- Marketing Cookies: ${report.consentChoices.marketing ? 'GRANTED' : 'DENIED'} (Legal Basis: ${report.legalBasis.marketing})

WITHDRAWAL HISTORY:
${report.withdrawalHistory.map(h => 
  `${new Date(h.timestamp).toISOString()}: ${h.action.toUpperCase()} - ${h.category}`
).join('\n')}

Generated: ${new Date().toISOString()}
    `.trim();
  }
}

// Hook for React components to use compliance features
export const useCookieCompliance = () => {
  return {
    generateReport: CookieComplianceManager.generateComplianceReport,
    recordAction: CookieComplianceManager.recordConsentAction,
    isValid: CookieComplianceManager.isConsentValid,
    getRights: CookieComplianceManager.getDataProtectionRights,
    exportData: CookieComplianceManager.exportCookieData,
    clearData: CookieComplianceManager.clearAllCookieData,
    getApplicableLaws: CookieComplianceManager.getApplicableLaws,
    generateProof: CookieComplianceManager.generateConsentProof
  };
};
