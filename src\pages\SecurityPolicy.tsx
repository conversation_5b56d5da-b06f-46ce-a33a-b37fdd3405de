import { useEffect } from "react";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { updatePageSEO } from "@/lib/seo";
import withPageLoader from "@/components/withPageLoader";
import { 
  Shield, 
  Lock, 
  Eye, 
  Server, 
  AlertTriangle,
  CheckCircle,
  Mail,
  FileText,
  Users,
  Database
} from "lucide-react";

const SecurityPolicy = () => {
  useEffect(() => {
    updatePageSEO('/security-policy');
  }, []);

  const securityMeasures = [
    {
      icon: Lock,
      title: "Data Encryption",
      description: "All data is encrypted in transit using TLS 1.3 and at rest using AES-256 encryption standards."
    },
    {
      icon: Server,
      title: "Secure Infrastructure",
      description: "Our systems are hosted on secure, regularly updated servers with 24/7 monitoring and intrusion detection."
    },
    {
      icon: Users,
      title: "Access Controls",
      description: "Multi-factor authentication and role-based access controls ensure only authorized personnel can access systems."
    },
    {
      icon: Database,
      title: "Data Protection",
      description: "Regular backups, data integrity checks, and secure data handling procedures protect your information."
    }
  ];

  const complianceStandards = [
    "ISO 27001 Information Security Management",
    "SOC 2 Type II Compliance",
    "GDPR (General Data Protection Regulation)",
    "Indian Personal Data Protection Act",
    "PCI DSS for Payment Processing",
    "OWASP Security Guidelines"
  ];

  const reportingProcess = [
    {
      step: "1",
      title: "Report Discovery",
      description: "Email <EMAIL> with detailed information about the vulnerability"
    },
    {
      step: "2", 
      title: "Initial Response",
      description: "We acknowledge receipt within 24 hours and begin initial assessment"
    },
    {
      step: "3",
      title: "Investigation",
      description: "Our security team investigates and validates the reported issue"
    },
    {
      step: "4",
      title: "Resolution",
      description: "We develop and deploy fixes, then notify the reporter of resolution"
    }
  ];

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      {/* Hero Section */}
      <section className="w-full bg-gradient-hero py-16 lg:py-24">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="text-center max-w-4xl mx-auto space-y-8">
            <Badge variant="secondary" className="text-sm font-medium bg-gray-100 text-gray-700 rounded-full px-4 py-2">
              <Shield className="w-4 h-4 mr-2" />
              Security & Trust
            </Badge>
            
            <h1 className="text-4xl lg:text-6xl font-bold text-foreground leading-tight">
              Security Policy
            </h1>
            
            <p className="text-lg text-muted-foreground leading-relaxed max-w-3xl mx-auto">
              At Eria Software, security is fundamental to everything we do. We implement comprehensive 
              security measures to protect your data and maintain the highest standards of information security.
            </p>

            <div className="text-sm text-muted-foreground">
              Last updated: January 2025 | Version 2.0
            </div>
          </div>
        </div>
      </section>

      {/* Security Overview */}
      <section className="w-full py-16 lg:py-24 bg-background">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <Card className="border-0 shadow-soft bg-green-50">
              <CardContent className="p-8">
                <div className="flex items-start gap-4">
                  <Shield className="w-8 h-8 text-green-600 mt-1" />
                  <div>
                    <h2 className="text-2xl font-bold text-green-900 mb-4">
                      Our Security Commitment
                    </h2>
                    <p className="text-green-800 leading-relaxed mb-4">
                      We are committed to maintaining the confidentiality, integrity, and availability of all 
                      information entrusted to us. Our security program is designed to protect against unauthorized 
                      access, use, disclosure, disruption, modification, or destruction of information.
                    </p>
                    <p className="text-green-800 leading-relaxed">
                      This policy outlines our security practices, incident response procedures, and compliance 
                      with industry standards and regulations.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Security Measures */}
      <section className="w-full py-16 lg:py-24 bg-muted/30">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-foreground mb-4">
              Security Measures
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              We implement multiple layers of security controls to protect your data and our systems.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            {securityMeasures.map((measure, index) => (
              <Card key={index} className="border-0 shadow-soft bg-background">
                <CardContent className="p-6">
                  <div className="flex items-start gap-4">
                    <div className="bg-blue-100 p-3 rounded-lg">
                      <measure.icon className="w-6 h-6 text-blue-600" />
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold text-foreground mb-3">
                        {measure.title}
                      </h3>
                      <p className="text-muted-foreground leading-relaxed">
                        {measure.description}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Compliance Standards */}
      <section className="w-full py-16 lg:py-24 bg-background">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-foreground mb-4">
              Compliance & Standards
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              We adhere to industry-leading security standards and regulatory requirements.
            </p>
          </div>

          <div className="max-w-3xl mx-auto">
            <Card className="border-0 shadow-soft bg-background">
              <CardContent className="p-8">
                <div className="grid md:grid-cols-2 gap-4">
                  {complianceStandards.map((standard, index) => (
                    <div key={index} className="flex items-center gap-3">
                      <CheckCircle className="w-5 h-5 text-green-600 flex-shrink-0" />
                      <span className="text-muted-foreground">{standard}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Vulnerability Reporting */}
      <section className="w-full py-16 lg:py-24 bg-muted/20">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-foreground mb-4">
              Responsible Disclosure
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              We welcome security researchers to help us maintain the security of our systems.
            </p>
          </div>

          <div className="max-w-4xl mx-auto space-y-8">
            <Card className="border-0 shadow-soft bg-orange-50">
              <CardContent className="p-6">
                <div className="flex items-start gap-4">
                  <AlertTriangle className="w-6 h-6 text-orange-600 mt-1" />
                  <div>
                    <h3 className="text-lg font-semibold text-orange-900 mb-2">
                      Security Vulnerability Reporting
                    </h3>
                    <p className="text-orange-800 text-sm">
                      If you discover a security vulnerability, please report it responsibly to 
                      <EMAIL>. Do not publicly disclose the issue until we have 
                      had a chance to address it.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
              {reportingProcess.map((process, index) => (
                <Card key={index} className="border-0 shadow-soft bg-background">
                  <CardContent className="p-6 text-center">
                    <div className="w-12 h-12 bg-blue-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-lg font-bold">
                      {process.step}
                    </div>
                    <h4 className="font-semibold text-foreground mb-2">
                      {process.title}
                    </h4>
                    <p className="text-sm text-muted-foreground">
                      {process.description}
                    </p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Contact Information */}
      <section className="w-full py-16 lg:py-24 bg-background">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="max-w-2xl mx-auto text-center">
            <Card className="border-0 shadow-soft bg-background">
              <CardContent className="p-8">
                <Mail className="w-12 h-12 text-blue-600 mx-auto mb-4" />
                <h2 className="text-2xl font-bold text-foreground mb-4">
                  Security Contact
                </h2>
                <p className="text-muted-foreground leading-relaxed mb-6">
                  For security-related inquiries, vulnerability reports, or compliance questions, 
                  please contact our security team.
                </p>
                
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium text-foreground mb-1">Security Team</h4>
                    <a 
                      href="mailto:<EMAIL>" 
                      className="text-blue-600 hover:text-blue-700 font-medium"
                    >
                      <EMAIL>
                    </a>
                  </div>
                  
                  <div>
                    <h4 className="font-medium text-foreground mb-1">General Inquiries</h4>
                    <a 
                      href="mailto:<EMAIL>" 
                      className="text-blue-600 hover:text-blue-700 font-medium"
                    >
                      <EMAIL>
                    </a>
                  </div>
                </div>

                <p className="text-sm text-muted-foreground mt-6">
                  We take all security reports seriously and will respond within 24 hours.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default withPageLoader(SecurityPolicy, {
  loadingText: "Loading Security Policy...",
  minLoadingTime: 600
});
