import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import withPageLoader from "@/components/withPageLoader";
import { 
  Smartphone, 
  Tablet, 
  Zap, 
  Bell, 
  CreditCard, 
  MapPin,
  Star,
  Users,
  ShoppingBag,
  CheckCircle
} from "lucide-react";

const AppDevelopment = () => {
  const features = [
    {
      icon: Smartphone,
      title: "नेटिव्ह मोबाइल अॅप्स",
      description: "नेटिव्ह कार्यप्रदर्शनासह आपल्या रेस्टॉरंटसाठी विशेषतः तयार केलेले iOS आणि Android अॅप्स."
    },
    {
      icon: ShoppingBag,
      title: "ऑनलाइन ऑर्डरिंग",
      description: "मेनू ब्राउझिंग, कस्टमायझेशन आणि चेकआउटसह निर्बाध ऑर्डरिंग अनुभव."
    },
    {
      icon: Bell,
      title: "पुश नोटिफिकेशन्स",
      description: "वैयक्तिकृत ऑफर, ऑर्डर अपडेट्स आणि प्रमोशनसह ग्राहकांना गुंतवून ठेवा."
    },
    {
      icon: CreditCard,
      title: "सुरक्षित पेमेंट्स",
      description: "सुरक्षित, PCI-अनुपालित प्रक्रियेसह अनेक पेमेंट पर्याय."
    },
    {
      icon: MapPin,
      title: "लोकेशन सेवा",
      description: "डिलिव्हरीसाठी GPS ट्रॅकिंग, स्टोअर लोकेटर आणि लोकेशन-आधारित ऑफर."
    },
    {
      icon: Star,
      title: "लॉयल्टी प्रोग्राम",
      description: "ग्राहक धारणा आणि पुनरावृत्ती ऑर्डर वाढवण्यासाठी अंगभूत रिवॉर्ड सिस्टम."
    }
  ];

  const appTypes = [
    {
      title: "Customer Ordering App",
      description: "Allow customers to browse menu, place orders, and track delivery",
      features: ["Menu browsing", "Order customization", "Payment integration", "Order tracking"]
    },
    {
      title: "Delivery Driver App",
      description: "Optimize delivery operations with driver management tools",
      features: ["Route optimization", "Real-time tracking", "Earnings dashboard", "Order management"]
    },
    {
      title: "Restaurant Management App",
      description: "Manage your restaurant operations on-the-go",
      features: ["Order management", "Inventory tracking", "Staff scheduling", "Analytics dashboard"]
    }
  ];

  const benefits = [
    "Increase orders by 40% with mobile app",
    "Reduce delivery time with optimized routing",
    "Build customer loyalty with rewards program",
    "Real-time order and delivery tracking",
    "Integrated with your existing POS system",
    "Custom branding and design"
  ];

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      {/* Hero Section */}
      <section className="w-full bg-gradient-hero py-16 lg:py-24">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-8">
              <div className="space-y-4">
                <Badge variant="secondary" className="text-sm font-medium bg-gray-100 text-gray-700 rounded-full px-4 py-2">
                  Custom Mobile App Development
                </Badge>
                
                <h1 className="text-4xl lg:text-6xl font-bold text-foreground leading-tight">
                  Restaurant Mobile Apps That Drive Revenue
                </h1>
                
                <p className="text-lg text-muted-foreground leading-relaxed max-w-lg">
                  Custom iOS and Android apps for your restaurant with online ordering, 
                  loyalty programs, and seamless POS integration to boost customer engagement.
                </p>
              </div>

              <Button variant="hero" size="lg" className="text-lg px-8 py-6 bg-blue-600 text-white hover:bg-blue-700">
                Get App Quote
              </Button>

              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-5 h-5 text-green-500" />
                  <span className="text-sm font-medium text-foreground">iOS & Android development</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-5 h-5 text-green-500" />
                  <span className="text-sm font-medium text-foreground">POS system integration</span>
                </div>
              </div>
            </div>

            <div className="relative">
              <img 
                src="https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=600&h=400&fit=crop" 
                alt="Mobile App Development" 
                className="w-full h-96 object-cover rounded-lg shadow-lg"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="w-full py-16 lg:py-24 bg-muted/30">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-foreground mb-4">
              Powerful Mobile App Features
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Everything you need to create an exceptional mobile experience for your customers.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <Card 
                key={index} 
                className="border-0 shadow-soft hover:shadow-lg transition-all duration-300 hover:scale-105 bg-background"
              >
                <CardContent className="p-8">
                  <div className="space-y-4">
                    <div className="w-12 h-12 bg-gradient-primary rounded-lg flex items-center justify-center">
                      <feature.icon className="w-6 h-6 text-primary-foreground" />
                    </div>
                    
                    <h3 className="text-xl font-semibold text-foreground">
                      {feature.title}
                    </h3>
                    
                    <p className="text-muted-foreground leading-relaxed">
                      {feature.description}
                    </p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* App Types Section */}
      <section className="w-full py-16 lg:py-24 bg-background">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-foreground mb-4">
              Complete App Ecosystem for Your Restaurant
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              We develop different types of apps to cover all aspects of your restaurant operations.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {appTypes.map((app, index) => (
              <Card key={index} className="border-0 shadow-soft hover:shadow-lg transition-all duration-300 bg-background">
                <CardContent className="p-8">
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-xl font-semibold text-foreground mb-2">
                        {app.title}
                      </h3>
                      <p className="text-muted-foreground">
                        {app.description}
                      </p>
                    </div>
                    
                    <div className="space-y-2">
                      {app.features.map((feature, featureIndex) => (
                        <div key={featureIndex} className="flex items-center gap-2">
                          <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                          <span className="text-sm text-foreground">{feature}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="w-full py-16 lg:py-24 bg-muted/20">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-8">
              <h2 className="text-3xl lg:text-4xl font-bold text-foreground">
                Why Your Restaurant Needs a Mobile App
              </h2>
              
              <div className="space-y-4">
                {benefits.map((benefit, index) => (
                  <div key={index} className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                    <span className="text-foreground">{benefit}</span>
                  </div>
                ))}
              </div>

              <Button variant="hero" size="lg" className="text-lg px-8 py-6 bg-blue-600 text-white hover:bg-blue-700">
                Start Your App Project
              </Button>
            </div>

            <div className="relative">
              <img 
                src="https://images.unsplash.com/photo-1551650975-87deedd944c3?w=600&h=400&fit=crop" 
                alt="Mobile App Benefits" 
                className="w-full h-96 object-cover rounded-lg shadow-lg"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="w-full py-16 lg:py-24 bg-background">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl lg:text-4xl font-bold text-foreground mb-4">
              Mobile Apps Drive Real Results
            </h2>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="text-4xl lg:text-5xl font-bold text-blue-600 mb-2">40%</div>
              <div className="text-lg font-medium text-foreground mb-2">More Orders</div>
              <div className="text-muted-foreground">Increase in orders through mobile app</div>
            </div>
            <div className="text-center">
              <div className="text-4xl lg:text-5xl font-bold text-blue-600 mb-2">60%</div>
              <div className="text-lg font-medium text-foreground mb-2">Customer Retention</div>
              <div className="text-muted-foreground">Higher retention with loyalty programs</div>
            </div>
            <div className="text-center">
              <div className="text-4xl lg:text-5xl font-bold text-blue-600 mb-2">25%</div>
              <div className="text-lg font-medium text-foreground mb-2">Faster Delivery</div>
              <div className="text-muted-foreground">Reduced delivery time with app optimization</div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="w-full py-16 lg:py-24 bg-black">
        <div className="container mx-auto px-6 lg:px-8 text-center">
          <div className="max-w-3xl mx-auto space-y-8">
            <h2 className="text-3xl lg:text-5xl font-bold text-white">
              Ready to Launch Your Restaurant App?
            </h2>
            
            <p className="text-lg lg:text-xl text-white/90 leading-relaxed">
              Get a custom mobile app that increases orders and builds customer loyalty.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Button
                variant="hero"
                size="lg"
                className="text-lg px-8 py-6 bg-blue-600 text-white hover:bg-blue-700"
              >
                Get App Quote
              </Button>

              <Button
                variant="outline"
                size="lg"
                className="text-lg px-8 py-6 bg-transparent border-2 border-white text-white hover:bg-white hover:text-black"
              >
                View App Demos
              </Button>
            </div>

            <div className="pt-8 text-sm text-white/80">
              ✓ iOS & Android • ✓ Custom design • ✓ POS integration • ✓ Ongoing support
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default withPageLoader(AppDevelopment, {
  loadingText: "Loading App Development...",
  minLoadingTime: 1000
});
