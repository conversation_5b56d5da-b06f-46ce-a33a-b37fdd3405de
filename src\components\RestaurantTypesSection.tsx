import { Card, CardContent } from "@/components/ui/card";

const RestaurantTypesSection = () => {
  const restaurantTypes = [
    {
      title: "फाइन डायनिंग आणि प्रीमियम बार",
      features: [
        "अतिथी प्राधान्यांसह प्रगत टेबल आरक्षण प्रणाली",
        "कोर्स टाइमिंग नियंत्रणांसह अत्याधुनिक वेटर अॅप",
        "वाइन पेअरिंग सूचना आणि प्रीमियम इन्व्हेंटरी ट्रॅकिंग",
        "तपशीलवार अतिथी इतिहास आणि वैयक्तिकृत सेवा नोट्स"
      ],
      image: "https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?w=600&h=400&fit=crop"
    },
    {
      title: "क्विक सर्व्हिस रेस्टॉरंट्स",
      features: [
        "30 सेकंदांपेक्षा कमी वेळेत विजेसारखी जलद ऑर्डर प्रक्रिया",
        "पीक अवर्ससाठी डायनॅमिक मेनू शेड्यूलिंग",
        "रिअल-टाइम अलर्टसह स्वयंचलित इन्व्हेंटरी कमी होणे",
        "कमी प्रतीक्षा वेळेसाठी सेल्फ-सर्व्हिस किऑस्क एकीकरण"
      ],
      image: "https://images.unsplash.com/photo-1571091718767-18b5b1457add?w=600&h=400&fit=crop"
    },
    {
      title: "क्लाउड किचन आणि व्हर्च्युअल ब्रँड्स",
      features: [
        "एकाच डॅशबोर्डवरून मल्टी-ब्रँड ऑर्डर व्यवस्थापन",
        "Zomato, Swiggy आणि UberEats सह निर्बाध एकीकरण",
        "अनेक व्हर्च्युअल ब्रँड्समध्ये एकीकृत इन्व्हेंटरी ट्रॅकिंग",
        "प्रति प्लॅटफॉर्म कमिशन ट्रॅकिंग आणि नफा विश्लेषण"
      ],
      image: "https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=600&h=400&fit=crop"
    },
    {
      title: "पब, बार, क्लब आणि लाउंजेस",
      features: [
        "उच्च-व्हॉल्यूम वातावरणासाठी मल्टी-टर्मिनल बिलिंग",
        "पोअर कॉस्ट ट्रॅकिंगसह प्रगत लिकर इन्व्हेंटरी",
        "गट आणि कार्यक्रमांसाठी लवचिक बिल स्प्लिटिंग",
        "हॅप्पी अवर किंमत आणि प्रचारात्मक व्यवस्थापन"
      ],
      image: "https://images.unsplash.com/photo-1514933651103-005eec06c04b?w=600&h=400&fit=crop"
    }
  ];

  return (
    <section className="w-full py-16 lg:py-24 bg-muted/20">
      <div className="container mx-auto px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="text-sm font-semibold text-primary mb-4 uppercase tracking-wide">
            रेस्टॉरंट प्रकार
          </div>
          <h2 className="text-3xl lg:text-5xl font-bold text-foreground mb-4">
            प्रत्येक रेस्टॉरंट फॉर्मॅटसाठी तयार केलेले समाधान
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            फाइन डायनिंग संस्थांपासून क्विक-सर्व्हिस रेस्टॉरंट्सपर्यंत, Zyka POS सर्व रेस्टॉरंट फॉर्मॅट्समध्ये कार्यक्षमता आणि नफा वाढवणार्‍या
            उद्योग-केंद्रित वैशिष्ट्यांसह आपल्या विशिष्ट व्यवसाय मॉडेलशी जुळवून घेते.
          </p>
        </div>

        {/* Restaurant Types Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-8">
          {restaurantTypes.map((type, index) => (
            <Card
              key={index}
              className="overflow-hidden shadow-soft hover:shadow-lg transition-all duration-300 md:hover:scale-105 bg-background border-0"
            >
              <CardContent className="p-0">
                <div className="relative">
                  <img
                    src={type.image}
                    alt={type.title}
                    className="w-full h-48 sm:h-56 md:h-64 object-cover"
                  />
                  <div className="absolute inset-0 bg-black/50"></div>

                  <div className="absolute bottom-4 left-4 right-4 sm:bottom-6 sm:left-6 sm:right-6 text-white">
                    <h3 className="text-lg sm:text-xl md:text-2xl font-bold mb-3 md:mb-4 leading-tight">
                      {type.title}
                    </h3>

                    <ul className="space-y-1.5 sm:space-y-2">
                      {type.features.map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-start gap-2">
                          <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-white rounded-full mt-1.5 sm:mt-2 flex-shrink-0"></div>
                          <span className="text-xs sm:text-sm leading-relaxed">{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default RestaurantTypesSection;