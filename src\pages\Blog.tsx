import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import withPageLoader from "@/components/withPageLoader";
import { Calendar, User, ArrowRight, Clock } from "lucide-react";

const Blog = () => {
  const blogPosts = [
    {
      title: "10 Ways ZYKA POS Can Transform Your Restaurant Operations",
      excerpt: "Discover how modern POS systems are revolutionizing restaurant management and boosting profitability.",
      author: "Eria Team",
      date: "January 15, 2025",
      readTime: "5 min read",
      category: "Restaurant Technology",
      image: "https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400&h=250&fit=crop"
    },
    {
      title: "The Future of Restaurant Technology: Trends to Watch in 2025",
      excerpt: "Explore the latest trends in restaurant technology and how they're shaping the industry.",
      author: "Tech Team",
      date: "January 10, 2025",
      readTime: "7 min read",
      category: "Industry Trends",
      image: "https://images.unsplash.com/photo-1551650975-87deedd944c3?w=400&h=250&fit=crop"
    },
    {
      title: "How to Choose the Right POS System for Your Restaurant",
      excerpt: "A comprehensive guide to selecting the perfect point-of-sale system for your business needs.",
      author: "Business Team",
      date: "January 5, 2025",
      readTime: "6 min read",
      category: "Buying Guide",
      image: "https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?w=400&h=250&fit=crop"
    },
    {
      title: "Inventory Management Best Practices for Restaurants",
      excerpt: "Learn proven strategies to optimize your restaurant inventory and reduce waste.",
      author: "Operations Team",
      date: "December 28, 2024",
      readTime: "8 min read",
      category: "Operations",
      image: "https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400&h=250&fit=crop"
    },
    {
      title: "Customer Success Story: How Mumbai Cafe Increased Revenue by 40%",
      excerpt: "Read how a local cafe transformed their business using ERIA POS solutions.",
      author: "Success Team",
      date: "December 20, 2024",
      readTime: "4 min read",
      category: "Success Stories",
      image: "https://images.unsplash.com/photo-1554118811-1e0d58224f24?w=400&h=250&fit=crop"
    },
    {
      title: "Mobile Apps for Restaurants: Why You Need One in 2025",
      excerpt: "Understand the importance of mobile apps for restaurant success and customer engagement.",
      author: "Mobile Team",
      date: "December 15, 2024",
      readTime: "5 min read",
      category: "Mobile Technology",
      image: "https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=400&h=250&fit=crop"
    }
  ];

  const categories = [
    "All Posts",
    "Restaurant Technology",
    "Industry Trends",
    "Buying Guide",
    "Operations",
    "Success Stories",
    "Mobile Technology"
  ];

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      {/* Hero Section */}
      <section className="w-full bg-gradient-hero py-16 lg:py-24">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="text-center max-w-4xl mx-auto space-y-8">
            <Badge variant="secondary" className="text-sm font-medium bg-gray-100 text-gray-700 rounded-full px-4 py-2">
              Knowledge Hub
            </Badge>
            
            <h1 className="text-4xl lg:text-6xl font-bold text-foreground leading-tight">
              Eria Software Blog
            </h1>
            
            <p className="text-lg text-muted-foreground leading-relaxed max-w-3xl mx-auto">
              Stay updated with the latest insights, tips, and trends in restaurant technology, 
              business operations, and industry best practices.
            </p>
          </div>
        </div>
      </section>

      {/* Categories */}
      <section className="w-full py-8 bg-muted/30">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="flex flex-wrap justify-center gap-4">
            {categories.map((category, index) => (
              <Button
                key={index}
                variant={index === 0 ? "default" : "outline"}
                size="sm"
                className={index === 0 ? "bg-blue-600 text-white hover:bg-blue-700" : ""}
              >
                {category}
              </Button>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Post */}
      <section className="w-full py-16 lg:py-24 bg-background">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl lg:text-4xl font-bold text-foreground mb-4">
                Featured Article
              </h2>
            </div>

            <Card className="border-0 shadow-soft bg-background overflow-hidden">
              <div className="grid lg:grid-cols-2 gap-0">
                <div className="relative">
                  <img 
                    src={blogPosts[0].image} 
                    alt={blogPosts[0].title}
                    className="w-full h-64 lg:h-full object-cover"
                  />
                  <Badge className="absolute top-4 left-4 bg-blue-600 text-white">
                    {blogPosts[0].category}
                  </Badge>
                </div>
                <CardContent className="p-8 lg:p-12 flex flex-col justify-center">
                  <div className="space-y-4">
                    <h3 className="text-2xl lg:text-3xl font-bold text-foreground leading-tight">
                      {blogPosts[0].title}
                    </h3>
                    
                    <p className="text-muted-foreground leading-relaxed">
                      {blogPosts[0].excerpt}
                    </p>

                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <User className="w-4 h-4" />
                        <span>{blogPosts[0].author}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Calendar className="w-4 h-4" />
                        <span>{blogPosts[0].date}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Clock className="w-4 h-4" />
                        <span>{blogPosts[0].readTime}</span>
                      </div>
                    </div>

                    <Button variant="link" className="p-0 font-semibold text-blue-600 hover:text-blue-700">
                      Read Full Article
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </Button>
                  </div>
                </CardContent>
              </div>
            </Card>
          </div>
        </div>
      </section>

      {/* Blog Posts Grid */}
      <section className="w-full py-16 lg:py-24 bg-muted/30">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-foreground mb-4">
              Latest Articles
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Explore our collection of articles covering restaurant technology, business tips, and industry insights.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {blogPosts.slice(1).map((post, index) => (
              <Card 
                key={index} 
                className="border-0 shadow-soft hover:shadow-lg transition-all duration-300 hover:scale-105 bg-background overflow-hidden"
              >
                <div className="relative">
                  <img 
                    src={post.image} 
                    alt={post.title}
                    className="w-full h-48 object-cover"
                  />
                  <Badge className="absolute top-4 left-4 bg-blue-600 text-white text-xs">
                    {post.category}
                  </Badge>
                </div>
                
                <CardContent className="p-6">
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-foreground leading-tight">
                      {post.title}
                    </h3>
                    
                    <p className="text-muted-foreground text-sm leading-relaxed">
                      {post.excerpt}
                    </p>

                    <div className="flex items-center justify-between text-xs text-muted-foreground">
                      <div className="flex items-center gap-3">
                        <span>{post.author}</span>
                        <span>{post.date}</span>
                      </div>
                      <span>{post.readTime}</span>
                    </div>

                    <Button variant="link" className="p-0 font-semibold text-blue-600 hover:text-blue-700 text-sm">
                      Read More
                      <ArrowRight className="w-3 h-3 ml-1" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Newsletter Signup */}
      <section className="w-full py-16 lg:py-24 bg-black">
        <div className="container mx-auto px-6 lg:px-8 text-center">
          <div className="max-w-3xl mx-auto space-y-8">
            <h2 className="text-3xl lg:text-5xl font-bold text-white">
              Stay Updated with Our Latest Insights
            </h2>
            
            <p className="text-lg lg:text-xl text-white/90 leading-relaxed">
              Subscribe to our newsletter and get the latest articles, tips, and industry news delivered to your inbox.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center max-w-md mx-auto">
              <input 
                type="email" 
                placeholder="Enter your email address"
                className="w-full px-4 py-3 rounded-md border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <Button
                variant="hero"
                size="lg"
                className="w-full sm:w-auto text-lg px-8 py-3 bg-blue-600 text-white hover:bg-blue-700"
              >
                Subscribe
              </Button>
            </div>

            <div className="pt-8 text-sm text-white/80">
              ✓ Weekly insights • ✓ Industry trends • ✓ Expert tips • ✓ No spam, unsubscribe anytime
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default withPageLoader(Blog, {
  loadingText: "Loading Blog...",
  minLoadingTime: 900
});
