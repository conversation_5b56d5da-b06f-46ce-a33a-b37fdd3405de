import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Shield, Cookie, Globe, Users, Lock, AlertCircle } from "lucide-react";
import { Link } from "react-router-dom";
import withPageLoader from "@/components/withPageLoader";

const PrivacyPolicy = () => {
  const sections = [
    {
      title: "Information We Collect",
      content: [
        "Personal information you provide when creating an account or using our services",
        "Contact form data including name, email, phone number, and inquiry details",
        "Demo booking information including scheduling preferences and business requirements",
        "Business information related to your restaurant operations",
        "Usage data and analytics to improve our services",
        "Payment and billing information for subscription management",
        "Communication records for customer support purposes",
        "Cookie and tracking data as described in our Cookie Policy"
      ]
    },
    {
      title: "Cookies and Tracking Technologies",
      content: [
        "We use cookies and similar technologies to enhance your experience and analyze usage",
        "Essential cookies are necessary for website functionality and cannot be disabled",
        "Optional cookies require your consent and can be managed through our preference center",
        "Third-party cookies may be used for analytics, marketing, and social media integration",
        "You can control cookie preferences at any time through our Cookie Policy page"
      ]
    },
    {
      title: "How We Use Your Information",
      content: [
        "Respond to contact form inquiries and provide customer support",
        "Schedule and conduct product demonstrations and consultations",
        "Provide and maintain our POS software and related services",
        "Process transactions and manage your account",
        "Improve our products and develop new features",
        "Send marketing communications (only with your explicit consent)",
        "Communicate with you about updates, support, and service-related information",
        "Ensure security and prevent fraud"
      ]
    },
    {
      title: "Information Sharing",
      content: [
        "We do not sell, trade, or rent your personal information to third parties",
        "Information may be shared with trusted service providers who assist in our operations",
        "Legal compliance: We may disclose information when required by law",
        "Business transfers: Information may be transferred in case of merger or acquisition",
        "With your consent: We may share information for other purposes with your explicit consent"
      ]
    },
    {
      title: "Data Security",
      content: [
        "Industry-standard encryption for data transmission and storage",
        "Regular security audits and vulnerability assessments",
        "Access controls and authentication measures",
        "Secure data centers with physical and digital protection",
        "Employee training on data protection and privacy practices"
      ]
    },
    {
      title: "Your Rights Under GDPR and Indian Data Protection Laws",
      content: [
        "Right to Access: Request information about the data we hold about you",
        "Right to Rectification: Request correction of inaccurate or incomplete data",
        "Right to Erasure: Request deletion of your personal data (subject to legal requirements)",
        "Right to Data Portability: Request a copy of your data in a structured format",
        "Right to Object: Object to processing of your data for marketing purposes",
        "Right to Withdraw Consent: Withdraw consent for cookies and data processing at any time",
        "Right to Lodge a Complaint: File complaints with relevant data protection authorities"
      ]
    },
    {
      title: "Consent and Form Data Processing",
      content: [
        "Contact forms: We process your data based on your explicit consent to respond to inquiries",
        "Demo bookings: We process scheduling data based on your consent for demonstration purposes",
        "Marketing communications: We only send promotional content with your explicit opt-in consent",
        "You can withdraw consent at any time <NAME_EMAIL>",
        "Withdrawal of consent does not affect the lawfulness of processing before withdrawal",
        "Essential service communications do not require consent as they are contractually necessary"
      ]
    },
    {
      title: "Legal Basis for Processing",
      content: [
        "Contractual necessity: Processing required to provide our services and respond to inquiries",
        "Legitimate interest: Processing for business operations and service improvement",
        "Consent: Processing based on your explicit consent (e.g., marketing communications, optional cookies)",
        "Legal obligation: Processing required to comply with applicable laws",
        "Vital interests: Processing necessary to protect health and safety"
      ]
    },
    {
      title: "Data Retention and International Transfers",
      content: [
        "We retain personal data only as long as necessary for the purposes outlined in this policy",
        "Data may be transferred internationally with appropriate safeguards in place",
        "We comply with data localization requirements under Indian data protection laws",
        "Cross-border transfers are conducted with adequate protection measures",
        "You can request information about data retention periods for specific data categories"
      ]
    },
    {
      title: "Cookies and Tracking",
      content: [
        "We use cookies to enhance your experience and analyze usage patterns",
        "Essential cookies are necessary for the website to function properly",
        "Analytics cookies help us understand how you use our services",
        "You can control cookie preferences through your browser settings",
        "Third-party cookies may be used for analytics and advertising purposes"
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      {/* Hero Section */}
      <section className="w-full bg-gradient-hero py-16 lg:py-24">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="text-center max-w-4xl mx-auto space-y-8">
            <Badge variant="secondary" className="text-sm font-medium bg-gray-100 text-gray-700 rounded-full px-4 py-2">
              Legal Information
            </Badge>
            
            <h1 className="text-4xl lg:text-6xl font-bold text-foreground leading-tight">
              Privacy Policy
            </h1>
            
            <p className="text-lg text-muted-foreground leading-relaxed max-w-3xl mx-auto">
              This Privacy Policy explains how Eria Software Solutions & Services Private Limited 
              collects, uses, and protects your information when you use our services.
            </p>

            <div className="text-sm text-muted-foreground">
              Last updated: January 2025
            </div>
          </div>
        </div>
      </section>

      {/* Introduction */}
      <section className="w-full py-16 lg:py-24 bg-background">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <Card className="border-0 shadow-soft bg-muted/20">
              <CardContent className="p-8">
                <h2 className="text-2xl font-bold text-foreground mb-4">
                  Our Commitment to Privacy
                </h2>
                <p className="text-muted-foreground leading-relaxed">
                  At Eria Software, we are committed to protecting your privacy and ensuring the security 
                  of your personal information. This Privacy Policy outlines our practices regarding the 
                  collection, use, and disclosure of information we receive from users of our ERIA POS 
                  software and related services. By using our services, you agree to the collection and 
                  use of information in accordance with this policy.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Privacy Sections */}
      <section className="w-full py-16 lg:py-24 bg-muted/30">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="max-w-4xl mx-auto space-y-8">
            {sections.map((section, index) => (
              <Card key={index} className="border-0 shadow-soft bg-background">
                <CardContent className="p-8">
                  <h3 className="text-xl font-semibold text-foreground mb-4">
                    {section.title}
                  </h3>
                  <ul className="space-y-3">
                    {section.content.map((item, itemIndex) => (
                      <li key={itemIndex} className="flex items-start gap-3">
                        <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div>
                        <span className="text-muted-foreground leading-relaxed">{item}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Data Retention */}
      <section className="w-full py-16 lg:py-24 bg-background">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <Card className="border-0 shadow-soft bg-background">
              <CardContent className="p-8">
                <h3 className="text-xl font-semibold text-foreground mb-4">
                  Data Retention
                </h3>
                <p className="text-muted-foreground leading-relaxed mb-4">
                  We retain your personal information only for as long as necessary to provide our services 
                  and fulfill the purposes outlined in this Privacy Policy. The retention period may vary 
                  depending on the type of information and legal requirements.
                </p>
                <ul className="space-y-2">
                  <li className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div>
                    <span className="text-muted-foreground">Account information: Retained while your account is active</span>
                  </li>
                  <li className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div>
                    <span className="text-muted-foreground">Transaction data: Retained for 7 years for tax and legal compliance</span>
                  </li>
                  <li className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div>
                    <span className="text-muted-foreground">Support communications: Retained for 3 years for quality assurance</span>
                  </li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Cookie Information */}
      <section className="w-full py-16 lg:py-24 bg-background">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <Card className="border-0 shadow-soft bg-blue-50">
              <CardContent className="p-8">
                <div className="flex items-start gap-4">
                  <Cookie className="w-8 h-8 text-blue-600 mt-1" />
                  <div>
                    <h2 className="text-2xl font-bold text-blue-900 mb-4">
                      Cookie Usage & Your Control
                    </h2>
                    <p className="text-blue-800 leading-relaxed mb-6">
                      We use cookies and similar technologies to provide essential functionality,
                      analyze usage patterns, and enhance your experience. You have full control
                      over optional cookies through our preference center.
                    </p>

                    <div className="grid md:grid-cols-2 gap-6 mb-6">
                      <div className="bg-white p-4 rounded-lg">
                        <h3 className="font-semibold text-blue-900 mb-2 flex items-center gap-2">
                          <Shield className="w-4 h-4" />
                          Essential Cookies
                        </h3>
                        <p className="text-sm text-blue-800">
                          Required for website functionality, security, and basic operations.
                          These cannot be disabled.
                        </p>
                      </div>
                      <div className="bg-white p-4 rounded-lg">
                        <h3 className="font-semibold text-blue-900 mb-2 flex items-center gap-2">
                          <Users className="w-4 h-4" />
                          Optional Cookies
                        </h3>
                        <p className="text-sm text-blue-800">
                          Used for analytics, marketing, and enhanced functionality.
                          Require your explicit consent.
                        </p>
                      </div>
                    </div>

                    <div className="flex flex-col sm:flex-row gap-4">
                      <Link to="/cookie-policy">
                        <Button className="bg-blue-600 text-white hover:bg-blue-700">
                          <Cookie className="w-4 h-4 mr-2" />
                          View Cookie Policy
                        </Button>
                      </Link>
                      <Button variant="outline" className="border-blue-600 text-blue-600 hover:bg-blue-50">
                        <AlertCircle className="w-4 h-4 mr-2" />
                        Manage Cookie Preferences
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Contact Information */}
      <section className="w-full py-16 lg:py-24 bg-muted/20">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <Card className="border-0 shadow-soft bg-background">
              <CardContent className="p-8">
                <h3 className="text-xl font-semibold text-foreground mb-4">
                  Contact Us About Privacy
                </h3>
                <p className="text-muted-foreground leading-relaxed mb-6">
                  If you have any questions about this Privacy Policy or our data practices, 
                  please contact us using the information below:
                </p>
                
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium text-foreground mb-1">Company:</h4>
                    <p className="text-muted-foreground">Eria Software Solutions & Services Private Limited</p>
                  </div>
                  
                  <div>
                    <h4 className="font-medium text-foreground mb-1">Email:</h4>
                    <p className="text-muted-foreground">
                      <a href="mailto:<EMAIL>" className="text-blue-600 hover:text-blue-700">
                        <EMAIL>
                      </a>
                    </p>
                  </div>
                  
                  <div>
                    <h4 className="font-medium text-foreground mb-1">Phone:</h4>
                    <p className="text-muted-foreground">
                      <a href="tel:+************" className="text-blue-600 hover:text-blue-700">
                        +91 9604069989
                      </a>
                    </p>
                  </div>
                  
                  <div>
                    <h4 className="font-medium text-foreground mb-1">Address:</h4>
                    <p className="text-muted-foreground">Mumbai, Maharashtra, India</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Updates */}
      <section className="w-full py-16 lg:py-24 bg-background">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <Card className="border-0 shadow-soft bg-blue-50">
              <CardContent className="p-8">
                <h3 className="text-xl font-semibold text-foreground mb-4">
                  Policy Updates
                </h3>
                <p className="text-muted-foreground leading-relaxed">
                  We may update this Privacy Policy from time to time to reflect changes in our practices 
                  or legal requirements. We will notify you of any material changes by posting the updated 
                  policy on our website and updating the "Last updated" date. We encourage you to review 
                  this Privacy Policy periodically to stay informed about how we protect your information.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default withPageLoader(PrivacyPolicy, {
  loadingText: "Loading Privacy Policy...",
  minLoadingTime: 600
});
