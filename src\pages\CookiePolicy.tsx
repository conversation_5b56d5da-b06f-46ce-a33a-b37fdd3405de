import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Shield, Clock, Globe, Users, AlertCircle, CheckCircle, Settings } from "lucide-react";
import { COOKIE_CATEGORIES, CookieManager } from "@/lib/utils";
import withPageLoader from "@/components/withPageLoader";
import { useState } from "react";
import CookiePreferenceCenter from "@/components/CookiePreferenceCenter";

const CookiePolicy = () => {
  const [showPreferences, setShowPreferences] = useState(false);

  const legalBasisInfo = [
    {
      title: "Consent (GDPR Art. 6(1)(a))",
      description: "For optional cookies like analytics and marketing, we rely on your explicit consent.",
      applies: ["Analytics Cookies", "Marketing Cookies"]
    },
    {
      title: "Legitimate Interest (GDPR Art. 6(1)(f))",
      description: "For functional cookies that improve your experience without being essential.",
      applies: ["Functional Cookies"]
    },
    {
      title: "Contractual Necessity (GDPR Art. 6(1)(b))",
      description: "For essential cookies required to provide our services.",
      applies: ["Essential Cookies"]
    }
  ];

  const userRights = [
    {
      icon: Shield,
      title: "Right to Access",
      description: "You can request information about what cookies we use and how we process your data."
    },
    {
      icon: Settings,
      title: "Right to Control",
      description: "You can accept, reject, or customize your cookie preferences at any time."
    },
    {
      icon: AlertCircle,
      title: "Right to Withdraw",
      description: "You can withdraw your consent for optional cookies without affecting website functionality."
    },
    {
      icon: Globe,
      title: "Right to Portability",
      description: "You can request your data in a structured, machine-readable format."
    }
  ];

  const handleManagePreferences = () => {
    setShowPreferences(true);
  };

  const handlePreferenceSave = (consent: any) => {
    CookieManager.setConsent(consent);
    setShowPreferences(false);
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      {/* Hero Section */}
      <section className="w-full bg-gradient-hero py-16 lg:py-24">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="text-center max-w-4xl mx-auto space-y-8">
            <Badge variant="secondary" className="text-sm font-medium bg-gray-100 text-gray-700 rounded-full px-4 py-2">
              <Shield className="w-4 h-4 mr-2" />
              Privacy & Compliance
            </Badge>

            <h1 className="text-4xl lg:text-6xl font-bold text-foreground leading-tight">
              Cookie Policy
            </h1>

            <p className="text-lg text-muted-foreground leading-relaxed max-w-3xl mx-auto">
              This policy explains how Eria Software Solutions & Services Private Limited uses cookies
              and similar technologies in compliance with GDPR, Indian data protection laws, and industry standards.
            </p>

            <div className="flex items-center justify-center gap-6 text-sm text-muted-foreground">
              <div className="flex items-center gap-2">
                <Clock className="w-4 h-4" />
                Last updated: January 2025
              </div>
              <div className="flex items-center gap-2">
                <Globe className="w-4 h-4" />
                Version 1.0
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* What are Cookies */}
      <section className="w-full py-16 lg:py-24 bg-background">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <Card className="border-0 shadow-soft bg-muted/20">
              <CardContent className="p-8">
                <h2 className="text-2xl font-bold text-foreground mb-4">
                  What Are Cookies?
                </h2>
                <p className="text-muted-foreground leading-relaxed mb-4">
                  Cookies are small text files that are stored on your device when you visit our website. 
                  They help us provide you with a better experience by remembering your preferences, 
                  analyzing how you use our site, and enabling certain functionality.
                </p>
                <p className="text-muted-foreground leading-relaxed mb-4">
                  We use both session cookies (which expire when you close your browser) and persistent
                  cookies (which remain on your device for a set period or until you delete them).
                </p>
                <p className="text-muted-foreground leading-relaxed">
                  <strong>Form Data Processing:</strong> When you submit our contact or demo forms, we process
                  your personal information based on your explicit consent. This processing is separate from
                  cookies but is governed by the same privacy principles outlined in our Privacy Policy.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Legal Compliance */}
      <section className="w-full py-16 lg:py-24 bg-muted/30">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <Card className="border-0 shadow-soft bg-blue-50">
              <CardContent className="p-8">
                <div className="flex items-start gap-4">
                  <Shield className="w-6 h-6 text-blue-600 mt-1" />
                  <div>
                    <h2 className="text-2xl font-bold text-blue-900 mb-4">
                      Legal Compliance & Your Rights
                    </h2>
                    <p className="text-blue-800 leading-relaxed mb-6">
                      We comply with the General Data Protection Regulation (GDPR), Indian Personal Data
                      Protection Act, and other applicable privacy laws. You have specific rights regarding
                      cookie usage and data processing.
                    </p>

                    <div className="grid md:grid-cols-2 gap-6 mb-6">
                      <div className="bg-white p-4 rounded-lg">
                        <h3 className="font-semibold text-blue-900 mb-2">GDPR Compliance</h3>
                        <p className="text-sm text-blue-800">
                          We obtain explicit consent for non-essential cookies and provide clear opt-out mechanisms
                          as required by European data protection law.
                        </p>
                      </div>
                      <div className="bg-white p-4 rounded-lg">
                        <h3 className="font-semibold text-blue-900 mb-2">Indian Data Protection</h3>
                        <p className="text-sm text-blue-800">
                          We follow Indian data localization and consent requirements for personal data
                          processing under applicable Indian laws.
                        </p>
                      </div>
                    </div>

                    <div className="grid md:grid-cols-3 gap-4">
                      {legalBasisInfo.map((basis, index) => (
                        <div key={index} className="bg-white p-4 rounded-lg">
                          <h4 className="font-medium text-blue-900 mb-2 text-sm">{basis.title}</h4>
                          <p className="text-xs text-blue-800 mb-2">{basis.description}</p>
                          <div className="text-xs text-blue-600">
                            <strong>Applies to:</strong> {basis.applies.join(", ")}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Types of Cookies */}
      <section className="w-full py-16 lg:py-24 bg-muted/30">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-foreground mb-4">
              Types of Cookies We Use
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              We use different types of cookies for various purposes to enhance your experience.
            </p>
          </div>

          <div className="max-w-4xl mx-auto space-y-8">
            {COOKIE_CATEGORIES.map((type, index) => (
              <Card key={index} className="border-0 shadow-soft bg-background">
                <CardContent className="p-8">
                  <div className="flex items-start justify-between mb-4">
                    <h3 className="text-xl font-semibold text-foreground">
                      {type.title}
                    </h3>
                    <Badge
                      variant={type.required ? "default" : "outline"}
                      className={type.required ? "bg-gray-600 text-white" : "text-blue-600 border-blue-600"}
                    >
                      {type.required ? "Required" : "Optional"}
                    </Badge>
                  </div>
                  
                  <p className="text-muted-foreground leading-relaxed mb-4">
                    {type.description}
                  </p>
                  
                  <div>
                    <h4 className="font-medium text-foreground mb-2">Cookies in this category:</h4>
                    <div className="space-y-3">
                      {type.cookies.map((cookie, cookieIndex) => (
                        <div key={cookieIndex} className="bg-muted/30 p-3 rounded-lg">
                          <div className="grid md:grid-cols-4 gap-3">
                            <div>
                              <h5 className="font-medium text-foreground text-sm">{cookie.name}</h5>
                              <p className="text-xs text-muted-foreground">{cookie.purpose}</p>
                            </div>
                            <div>
                              <span className="text-xs font-medium text-muted-foreground uppercase">Duration</span>
                              <p className="text-sm text-foreground">{cookie.duration}</p>
                            </div>
                            <div>
                              <span className="text-xs font-medium text-muted-foreground uppercase">Type</span>
                              <p className="text-sm text-foreground capitalize">{cookie.type}</p>
                            </div>
                            <div>
                              <span className="text-xs font-medium text-muted-foreground uppercase">Provider</span>
                              <p className="text-sm text-foreground">{cookie.provider}</p>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Your Rights */}
      <section className="w-full py-16 lg:py-24 bg-muted/30">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-foreground mb-4">
              Your Privacy Rights
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Under GDPR and Indian data protection laws, you have specific rights regarding cookie usage and data processing.
            </p>
          </div>

          <div className="max-w-4xl mx-auto grid md:grid-cols-2 gap-8">
            {userRights.map((right, index) => (
              <Card key={index} className="border-0 shadow-soft bg-background">
                <CardContent className="p-6">
                  <div className="flex items-start gap-4">
                    <div className="bg-blue-100 p-3 rounded-lg">
                      <right.icon className="w-6 h-6 text-blue-600" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-foreground mb-2">
                        {right.title}
                      </h3>
                      <p className="text-muted-foreground leading-relaxed">
                        {right.description}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="max-w-2xl mx-auto mt-12">
            <Card className="border-0 shadow-soft bg-green-50">
              <CardContent className="p-6 text-center">
                <CheckCircle className="w-8 h-8 text-green-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-green-900 mb-2">
                  Exercise Your Rights
                </h3>
                <p className="text-green-800 mb-4">
                  To exercise any of these rights or if you have questions about our cookie practices,
                  contact <NAME_EMAIL> or use our preference center.
                </p>
                <Button
                  onClick={handleManagePreferences}
                  className="bg-green-600 text-white hover:bg-green-700"
                >
                  <Settings className="w-4 h-4 mr-2" />
                  Manage Cookie Preferences
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Third-Party Cookies */}
      <section className="w-full py-16 lg:py-24 bg-background">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <Card className="border-0 shadow-soft bg-background">
              <CardContent className="p-8">
                <h3 className="text-xl font-semibold text-foreground mb-4">
                  Third-Party Cookies
                </h3>
                <p className="text-muted-foreground leading-relaxed mb-4">
                  We may also use third-party cookies from trusted partners to enhance our services:
                </p>
                <ul className="space-y-2 mb-6">
                  <li className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div>
                    <span className="text-muted-foreground"><strong>Google Analytics:</strong> To analyze website traffic and user behavior</span>
                  </li>
                  <li className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div>
                    <span className="text-muted-foreground"><strong>Social Media Platforms:</strong> For social sharing and login functionality</span>
                  </li>
                  <li className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div>
                    <span className="text-muted-foreground"><strong>Payment Processors:</strong> For secure payment processing</span>
                  </li>
                  <li className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div>
                    <span className="text-muted-foreground"><strong>Customer Support Tools:</strong> For live chat and support functionality</span>
                  </li>
                </ul>
                <p className="text-muted-foreground leading-relaxed">
                  These third-party services have their own privacy policies and cookie practices, 
                  which we encourage you to review.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Managing Cookies */}
      <section className="w-full py-16 lg:py-24 bg-muted/20">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <Card className="border-0 shadow-soft bg-background">
              <CardContent className="p-8">
                <h3 className="text-xl font-semibold text-foreground mb-4">
                  Managing Your Cookie Preferences
                </h3>
                <p className="text-muted-foreground leading-relaxed mb-6">
                  You have several options for managing cookies on our website:
                </p>
                
                <div className="space-y-6">
                  <div>
                    <h4 className="font-medium text-foreground mb-2">Browser Settings</h4>
                    <p className="text-muted-foreground text-sm mb-2">
                      Most browsers allow you to control cookies through their settings:
                    </p>
                    <ul className="space-y-1 ml-4">
                      <li className="text-muted-foreground text-sm">• Block all cookies</li>
                      <li className="text-muted-foreground text-sm">• Block third-party cookies only</li>
                      <li className="text-muted-foreground text-sm">• Delete existing cookies</li>
                      <li className="text-muted-foreground text-sm">• Set cookies to expire when you close your browser</li>
                    </ul>
                  </div>

                  <div>
                    <h4 className="font-medium text-foreground mb-2">Cookie Consent Banner</h4>
                    <p className="text-muted-foreground text-sm">
                      When you first visit our website, you'll see a cookie consent banner where you can 
                      choose which types of cookies to accept or reject.
                    </p>
                  </div>

                  <div>
                    <h4 className="font-medium text-foreground mb-2">Opt-Out Links</h4>
                    <p className="text-muted-foreground text-sm mb-2">
                      You can opt out of certain third-party cookies:
                    </p>
                    <ul className="space-y-1 ml-4">
                      <li className="text-muted-foreground text-sm">
                        • <a href="https://tools.google.com/dlpage/gaoptout" className="text-blue-600 hover:text-blue-700">Google Analytics Opt-out</a>
                      </li>
                      <li className="text-muted-foreground text-sm">
                        • <a href="http://optout.networkadvertising.org/" className="text-blue-600 hover:text-blue-700">Network Advertising Initiative</a>
                      </li>
                    </ul>
                  </div>
                </div>

                <div className="mt-8 p-4 bg-yellow-50 rounded-lg">
                  <p className="text-sm text-muted-foreground">
                    <strong>Note:</strong> Disabling certain cookies may affect the functionality of our website 
                    and limit your ability to use some features.
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Cookie Preferences */}
      <section className="w-full py-16 lg:py-24 bg-background">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="max-w-3xl mx-auto">
            <Card className="border-0 shadow-soft bg-blue-50">
              <CardContent className="p-8">
                <div className="text-center mb-6">
                  <Settings className="w-12 h-12 text-blue-600 mx-auto mb-4" />
                  <h2 className="text-2xl font-bold text-blue-900 mb-2">
                    Cookie Preference Center
                  </h2>
                  <p className="text-blue-800 leading-relaxed">
                    Manage your cookie preferences and control how we use cookies on our website.
                    Your choices will be saved and respected across all your visits.
                  </p>
                </div>

                <div className="grid md:grid-cols-2 gap-6 mb-8">
                  <div className="bg-white p-4 rounded-lg">
                    <h3 className="font-semibold text-blue-900 mb-2">Granular Control</h3>
                    <p className="text-sm text-blue-800">
                      Choose exactly which types of cookies you want to allow or block.
                    </p>
                  </div>
                  <div className="bg-white p-4 rounded-lg">
                    <h3 className="font-semibold text-blue-900 mb-2">Always Accessible</h3>
                    <p className="text-sm text-blue-800">
                      Change your preferences anytime from this page or our cookie banner.
                    </p>
                  </div>
                </div>

                <div className="text-center">
                  <Button
                    onClick={handleManagePreferences}
                    size="lg"
                    className="text-lg px-8 py-6 bg-blue-600 text-white hover:bg-blue-700"
                  >
                    <Settings className="w-5 h-5 mr-2" />
                    Open Preference Center
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Contact Information */}
      <section className="w-full py-16 lg:py-24 bg-muted/20">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <Card className="border-0 shadow-soft bg-background">
              <CardContent className="p-8">
                <h3 className="text-xl font-semibold text-foreground mb-4">
                  Questions About Cookies?
                </h3>
                <p className="text-muted-foreground leading-relaxed mb-6">
                  If you have any questions about our use of cookies, please contact us:
                </p>
                
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium text-foreground mb-1">Email:</h4>
                    <p className="text-muted-foreground">
                      <a href="mailto:<EMAIL>" className="text-blue-600 hover:text-blue-700">
                        <EMAIL>
                      </a>
                    </p>
                  </div>
                  
                  <div>
                    <h4 className="font-medium text-foreground mb-1">Phone:</h4>
                    <p className="text-muted-foreground">
                      <a href="tel:+919604069989" className="text-blue-600 hover:text-blue-700">
                        +91 9604069989
                      </a>
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      <Footer />

      <CookiePreferenceCenter
        isOpen={showPreferences}
        onClose={() => setShowPreferences(false)}
        onSave={handlePreferenceSave}
      />
    </div>
  );
};

export default withPageLoader(CookiePolicy, {
  loadingText: "Loading Cookie Policy...",
  minLoadingTime: 600
});
