import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import withPageLoader from "@/components/withPageLoader";
import { 
  Package, 
  TrendingDown, 
  AlertTriangle, 
  BarChart3, 
  RefreshCw, 
  Shield,
  Smartphone,
  Clock,
  DollarSign,
  CheckCircle
} from "lucide-react";

const Inventory = () => {
  const features = [
    {
      icon: Package,
      title: "रिअल-टाइम ट्रॅकिंग",
      description: "स्वयंचलित अपडेट्ससह सर्व स्थानांवर स्टॉक पातळी रिअल-टाइममध्ये निरीक्षण करा."
    },
    {
      icon: AlertTriangle,
      title: "स्मार्ट अलर्ट्स",
      description: "आयटम कमी होत असताना किंवा कालबाह्य होण्याच्या जवळ असताना सूचना मिळवा."
    },
    {
      icon: TrendingDown,
      title: "कचरा कमी करणे",
      description: "बुद्धिमान इन्व्हेंटरी अंतर्दृष्टीसह अन्न कचरा 25% पर्यंत कमी करा."
    },
    {
      icon: BarChart3,
      title: "विश्लेषण डॅशबोर्ड",
      description: "इन्व्हेंटरी टर्नओव्हर आणि खर्च विश्लेषणावर व्यापक अहवाल."
    },
    {
      icon: RefreshCw,
      title: "ऑटो रीऑर्डरिंग",
      description: "वापर पॅटर्नवर आधारित स्वयंचलित खरेदी ऑर्डर."
    },
    {
      icon: DollarSign,
      title: "खर्च नियंत्रण",
      description: "घटक खर्च ट्रॅक करा आणि जास्तीत जास्त नफ्यासाठी मेनू किंमत ऑप्टिमाइझ करा."
    }
  ];

  const benefits = [
    "Reduce inventory costs by 30%",
    "Eliminate stockouts completely",
    "Automated supplier management",
    "Recipe costing and analysis",
    "Multi-location inventory sync",
    "Expiry date tracking and alerts"
  ];

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      {/* Hero Section */}
      <section className="w-full bg-gradient-hero py-16 lg:py-24">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-8">
              <div className="space-y-4">
                <Badge variant="secondary" className="text-sm font-medium bg-gray-100 text-gray-700 rounded-full px-4 py-2">
                  Smart Inventory Management
                </Badge>
                
                <h1 className="text-4xl lg:text-6xl font-bold text-foreground leading-tight">
                  AI-Powered Inventory Control for Restaurants
                </h1>
                
                <p className="text-lg text-muted-foreground leading-relaxed max-w-lg">
                  Eliminate waste, reduce costs, and never run out of ingredients with our 
                  intelligent inventory management system that learns your business patterns.
                </p>
              </div>

              <Button
                variant="hero"
                size="lg"
                className="text-lg px-8 py-6 bg-blue-600 text-white hover:bg-blue-700"
                onClick={() => window.open('https://zykapos.eriasoftware.com', '_blank')}
              >
                Start Free Trial
              </Button>

              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-5 h-5 text-green-500" />
                  <span className="text-sm font-medium text-foreground">Reduce costs by 30%</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-5 h-5 text-green-500" />
                  <span className="text-sm font-medium text-foreground">Zero stockouts guaranteed</span>
                </div>
              </div>
            </div>

            <div className="relative">
              <img 
                src="https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=600&h=400&fit=crop" 
                alt="Inventory Management Dashboard" 
                className="w-full h-96 object-cover rounded-lg shadow-lg"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="w-full py-16 lg:py-24 bg-muted/30">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-foreground mb-4">
              Complete Inventory Management Solution
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              From procurement to consumption, manage every aspect of your restaurant inventory.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <Card 
                key={index} 
                className="border-0 shadow-soft hover:shadow-lg transition-all duration-300 hover:scale-105 bg-background"
              >
                <CardContent className="p-8">
                  <div className="space-y-4">
                    <div className="w-12 h-12 bg-gradient-primary rounded-lg flex items-center justify-center">
                      <feature.icon className="w-6 h-6 text-primary-foreground" />
                    </div>
                    
                    <h3 className="text-xl font-semibold text-foreground">
                      {feature.title}
                    </h3>
                    
                    <p className="text-muted-foreground leading-relaxed">
                      {feature.description}
                    </p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="w-full py-16 lg:py-24 bg-background">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-8">
              <h2 className="text-3xl lg:text-4xl font-bold text-foreground">
                Transform Your Inventory Management
              </h2>
              
              <div className="space-y-4">
                {benefits.map((benefit, index) => (
                  <div key={index} className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                    <span className="text-foreground">{benefit}</span>
                  </div>
                ))}
              </div>

              <Button variant="hero" size="lg" className="text-lg px-8 py-6 bg-blue-600 text-white hover:bg-blue-700">
                Get Demo
              </Button>
            </div>

            <div className="relative">
              <img 
                src="https://images.unsplash.com/photo-1586528116311-ad8dd3c8310d?w=600&h=400&fit=crop" 
                alt="Restaurant Inventory Benefits" 
                className="w-full h-96 object-cover rounded-lg shadow-lg"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="w-full py-16 lg:py-24 bg-muted/20">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl lg:text-4xl font-bold text-foreground mb-4">
              Proven Results for Restaurant Owners
            </h2>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="text-4xl lg:text-5xl font-bold text-blue-600 mb-2">30%</div>
              <div className="text-lg font-medium text-foreground mb-2">Cost Reduction</div>
              <div className="text-muted-foreground">Average inventory cost savings</div>
            </div>
            <div className="text-center">
              <div className="text-4xl lg:text-5xl font-bold text-blue-600 mb-2">25%</div>
              <div className="text-lg font-medium text-foreground mb-2">Waste Reduction</div>
              <div className="text-muted-foreground">Less food waste with smart tracking</div>
            </div>
            <div className="text-center">
              <div className="text-4xl lg:text-5xl font-bold text-blue-600 mb-2">99%</div>
              <div className="text-lg font-medium text-foreground mb-2">Stockout Prevention</div>
              <div className="text-muted-foreground">Never run out of key ingredients</div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="w-full py-16 lg:py-24 bg-black">
        <div className="container mx-auto px-6 lg:px-8 text-center">
          <div className="max-w-3xl mx-auto space-y-8">
            <h2 className="text-3xl lg:text-5xl font-bold text-white">
              Ready to Optimize Your Inventory?
            </h2>
            
            <p className="text-lg lg:text-xl text-white/90 leading-relaxed">
              Start reducing costs and eliminating waste with ERIA's intelligent inventory system.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Button
                variant="hero"
                size="lg"
                className="text-lg px-8 py-6 bg-blue-600 text-white hover:bg-blue-700"
                onClick={() => window.open('https://zykapos.eriasoftware.com', '_blank')}
              >
                Start Free Trial
              </Button>

              <Button
                variant="outline"
                size="lg"
                className="text-lg px-8 py-6 bg-transparent border-2 border-white text-white hover:bg-white hover:text-black"
                onClick={() => window.open('https://zykapos.eriasoftware.com', '_blank')}
              >
                Schedule Demo
              </Button>
            </div>

            <div className="pt-8 text-sm text-white/80">
              ✓ Free setup & training • ✓ 30-day money-back guarantee • ✓ 24/7 support included
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default withPageLoader(Inventory, {
  loadingText: "Loading Inventory Management...",
  minLoadingTime: 1000
});
