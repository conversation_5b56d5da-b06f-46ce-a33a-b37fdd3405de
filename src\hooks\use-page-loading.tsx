import { useState, useEffect } from "react";

interface UsePageLoadingOptions {
  minLoadingTime?: number; // Minimum loading time in milliseconds
  initialDelay?: number; // Initial delay before showing content
}

export const usePageLoading = (options: UsePageLoadingOptions = {}) => {
  const { minLoadingTime = 800, initialDelay = 200 } = options;
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const startTime = Date.now();

    // Initial delay to show loading state
    const initialTimer = setTimeout(() => {
      const elapsedTime = Date.now() - startTime;
      const remainingTime = Math.max(0, minLoadingTime - elapsedTime);

      // Ensure minimum loading time is met
      setTimeout(() => {
        setIsLoading(false);
      }, remainingTime);
    }, initialDelay);

    return () => {
      clearTimeout(initialTimer);
    };
  }, [minLoadingTime, initialDelay]);

  return { isLoading };
};
