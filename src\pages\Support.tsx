import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import withPageLoader from "@/components/withPageLoader";
import { 
  Headphones, 
  MessageCircle, 
  Mail, 
  Phone, 
  Clock, 
  FileText,
  Video,
  Users,
  CheckCircle,
  ArrowRight
} from "lucide-react";

const Support = () => {
  const supportOptions = [
    {
      icon: Phone,
      title: "Phone Support",
      description: "Get immediate help from our technical experts",
      contact: "+91 9604069989",
      availability: "Mon-Sat, 9 AM - 6 PM",
      response: "Immediate"
    },
    {
      icon: Mail,
      title: "Email Support",
      description: "Send us your questions and get detailed responses",
      contact: "<EMAIL>",
      availability: "24/7",
      response: "Within 2 hours"
    },
    {
      icon: MessageCircle,
      title: "Live Chat",
      description: "Chat with our support team in real-time",
      contact: "Available on website",
      availability: "Mon-Sat, 9 AM - 6 PM",
      response: "Instant"
    }
  ];

  const supportCategories = [
    {
      icon: FileText,
      title: "Technical Documentation",
      description: "Comprehensive guides and API documentation",
      items: ["Setup Guides", "User Manuals", "API Documentation", "Troubleshooting"]
    },
    {
      icon: Video,
      title: "Video Tutorials",
      description: "Step-by-step video guides for all features",
      items: ["Getting Started", "Advanced Features", "Integration Guides", "Best Practices"]
    },
    {
      icon: Users,
      title: "Training & Onboarding",
      description: "Personalized training for your team",
      items: ["Live Training Sessions", "Custom Workshops", "Staff Onboarding", "Certification Programs"]
    }
  ];

  const faqs = [
    {
      question: "How do I get started with ZYKA POS?",
      answer: "Contact our team for a free demo and consultation. We'll help you set up the system and provide comprehensive training for your staff."
    },
    {
      question: "What kind of support do you provide?",
      answer: "We offer 24/7 email support, phone support during business hours, live chat, comprehensive documentation, and personalized training sessions."
    },
    {
      question: "Is there a setup fee?",
      answer: "No, we provide free setup and installation. Our team will help you get started without any additional charges."
    },
    {
      question: "How quickly can I get help if I have an issue?",
      answer: "For urgent issues, call us directly for immediate assistance. Email support typically responds within 2 hours, and live chat provides instant responses during business hours."
    }
  ];

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      {/* Hero Section */}
      <section className="w-full bg-gradient-hero py-16 lg:py-24">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="text-center max-w-4xl mx-auto space-y-8">
            <Badge variant="secondary" className="text-sm font-medium bg-gray-100 text-gray-700 rounded-full px-4 py-2">
              24/7 Support Available
            </Badge>
            
            <h1 className="text-4xl lg:text-6xl font-bold text-foreground leading-tight">
              We're Here to Help You Succeed
            </h1>
            
            <p className="text-lg text-muted-foreground leading-relaxed max-w-3xl mx-auto">
              Get expert support, comprehensive documentation, and personalized training 
              to make the most of your ERIA POS system and grow your business.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Button variant="hero" size="lg" className="text-lg px-8 py-6 bg-blue-600 text-white hover:bg-blue-700">
                <Phone className="w-5 h-5 mr-2" />
                Call Support
              </Button>
              <Button variant="outline" size="lg" className="text-lg px-8 py-6">
                <Mail className="w-5 h-5 mr-2" />
                Email Us
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Support Options */}
      <section className="w-full py-16 lg:py-24 bg-muted/30">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-foreground mb-4">
              Multiple Support Channels
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Choose the support method that works best for you.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {supportOptions.map((option, index) => (
              <Card 
                key={index} 
                className="border-0 shadow-soft hover:shadow-lg transition-all duration-300 hover:scale-105 bg-background"
              >
                <CardContent className="p-8">
                  <div className="space-y-4">
                    <div className="w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center">
                      <option.icon className="w-8 h-8 text-primary-foreground" />
                    </div>
                    
                    <h3 className="text-xl font-semibold text-foreground">
                      {option.title}
                    </h3>
                    
                    <p className="text-muted-foreground leading-relaxed">
                      {option.description}
                    </p>

                    <div className="space-y-2 text-sm">
                      <div className="flex items-center gap-2">
                        <CheckCircle className="w-4 h-4 text-green-500" />
                        <span className="text-foreground">{option.contact}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Clock className="w-4 h-4 text-blue-600" />
                        <span className="text-muted-foreground">{option.availability}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <ArrowRight className="w-4 h-4 text-blue-600" />
                        <span className="text-muted-foreground">Response: {option.response}</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Support Categories */}
      <section className="w-full py-16 lg:py-24 bg-background">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-foreground mb-4">
              Comprehensive Support Resources
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Access a wide range of resources to help you succeed with ERIA POS.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {supportCategories.map((category, index) => (
              <Card key={index} className="border-0 shadow-soft bg-background">
                <CardContent className="p-8">
                  <div className="space-y-6">
                    <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center">
                      <category.icon className="w-8 h-8 text-white" />
                    </div>
                    
                    <div>
                      <h3 className="text-xl font-semibold text-foreground mb-2">
                        {category.title}
                      </h3>
                      <p className="text-muted-foreground mb-4">
                        {category.description}
                      </p>
                    </div>
                    
                    <div className="space-y-2">
                      {category.items.map((item, itemIndex) => (
                        <div key={itemIndex} className="flex items-center gap-2">
                          <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                          <span className="text-sm text-foreground">{item}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="w-full py-16 lg:py-24 bg-muted/20">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-foreground mb-4">
              Frequently Asked Questions
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Find quick answers to common questions about ERIA POS and our services.
            </p>
          </div>

          <div className="max-w-4xl mx-auto space-y-6">
            {faqs.map((faq, index) => (
              <Card key={index} className="border-0 shadow-soft bg-background">
                <CardContent className="p-8">
                  <h3 className="text-lg font-semibold text-foreground mb-3">
                    {faq.question}
                  </h3>
                  <p className="text-muted-foreground leading-relaxed">
                    {faq.answer}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Emergency Support */}
      <section className="w-full py-16 lg:py-24 bg-red-50">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="text-center max-w-3xl mx-auto">
            <div className="w-16 h-16 bg-red-600 rounded-full flex items-center justify-center mx-auto mb-6">
              <Headphones className="w-8 h-8 text-white" />
            </div>
            
            <h2 className="text-3xl lg:text-4xl font-bold text-foreground mb-4">
              Need Urgent Help?
            </h2>
            
            <p className="text-lg text-muted-foreground mb-8">
              For critical issues that affect your business operations, contact our emergency support line.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Button
                variant="hero"
                size="lg"
                className="text-lg px-8 py-6 bg-red-600 text-white hover:bg-red-700"
              >
                <Phone className="w-5 h-5 mr-2" />
                Emergency Support: +91 9604069989
              </Button>
            </div>

            <p className="text-sm text-muted-foreground mt-4">
              Available 24/7 for critical system issues
            </p>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="w-full py-16 lg:py-24 bg-black">
        <div className="container mx-auto px-6 lg:px-8 text-center">
          <div className="max-w-3xl mx-auto space-y-8">
            <h2 className="text-3xl lg:text-5xl font-bold text-white">
              Still Need Help?
            </h2>
            
            <p className="text-lg lg:text-xl text-white/90 leading-relaxed">
              Our support team is ready to assist you with any questions or issues.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Button
                variant="hero"
                size="lg"
                className="text-lg px-8 py-6 bg-blue-600 text-white hover:bg-blue-700"
              >
                Contact Support
              </Button>

              <Button
                variant="outline"
                size="lg"
                className="text-lg px-8 py-6 bg-transparent border-2 border-white text-white hover:bg-white hover:text-black"
              >
                Schedule Training
              </Button>
            </div>

            <div className="pt-8 text-sm text-white/80">
              ✓ Expert support • ✓ Quick resolution • ✓ Comprehensive training • ✓ 24/7 availability
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default withPageLoader(Support, {
  loadingText: "Loading Support...",
  minLoadingTime: 800
});
