# 🍽️ Eria Software Website

A modern, responsive website for **Eria Software Solutions & Services Private Limited** - a leading provider of restaurant management software and POS systems. Built with cutting-edge technologies and industry best practices.

![Eria Software](https://img.shields.io/badge/Eria-Software-blue?style=for-the-badge)
![React](https://img.shields.io/badge/React-18-61DAFB?style=for-the-badge&logo=react)
![TypeScript](https://img.shields.io/badge/TypeScript-5-3178C6?style=for-the-badge&logo=typescript)
![Tailwind CSS](https://img.shields.io/badge/Tailwind-CSS-38B2AC?style=for-the-badge&logo=tailwind-css)
![Vite](https://img.shields.io/badge/Vite-5-646CFF?style=for-the-badge&logo=vite)

## 🚀 Live Demo

**Production URL**: [https://eriasoftware.com](https://eriasoftware.com)

## 📋 Table of Contents

- [Features](#-features)
- [Tech Stack](#️-tech-stack)
- [Quick Start](#-quick-start)
- [Project Structure](#-project-structure)
- [Deployment](#-deployment)
- [Security Features](#-security-features)
- [Cookie & Privacy Compliance](#-cookie--privacy-compliance)
- [Accessibility Features](#-accessibility-features)
- [SEO Optimization](#-seo-optimization)
- [Contributing](#-contributing)
- [Support & Contact](#-support--contact)
- [Legal & Compliance](#-legal--compliance)

## 🏢 About Eria Software

**Eria Software Solutions & Services Private Limited** is a leading provider of restaurant management software and POS systems, helping restaurants streamline operations and boost efficiency.

### 📞 Contact Information

- **Phone**: +91-**********
- **Email**: <EMAIL>
- **Support**: <EMAIL>
- **Website**: https://eriasoftware.com

## ✨ Features

### 🎨 **Modern Design & UX**
- Clean, professional interface with smooth animations
- Responsive layout optimized for all devices and screen sizes
- Dark/light theme support with system preference detection
- Intuitive navigation with mobile-friendly hamburger menu

### ⚡ **Performance & SEO**
- Lightning-fast development and builds with Vite
- Comprehensive SEO optimization with meta tags and structured data
- Auto-generated sitemap.xml and robots.txt
- Optimized images and lazy loading for better performance
- Core Web Vitals optimized

### 🔐 **Security & Compliance**
- **GDPR & Indian Data Protection Compliant** cookie management
- Comprehensive input validation and XSS protection
- reCAPTCHA integration for spam protection
- Security headers (CSP, X-Frame-Options, HSTS)
- Rate limiting and suspicious activity detection

### ♿ **Accessibility**
- **WCAG 2.1 Level AA Compliant** design
- Full keyboard navigation support
- Screen reader compatibility with semantic HTML and ARIA labels
- High contrast color schemes
- Scalable text and UI elements

### 📱 **Contact & Demo System**
- Integrated contact forms with validation
- Demo booking system with Cal.com integration
- Email notifications via Formspree
- CAPTCHA protection against spam
- Consent management for data processing

### 📄 **Comprehensive Pages**
- **Home**: Hero section with company overview and services
- **Point of Sale**: POS system features and benefits
- **Inventory**: Inventory management solutions
- **Services**: Website development, app development, CRM integration
- **About Us**: Company information and team details
- **Contact**: Contact forms with reCAPTCHA protection
- **Demo**: Cal.com integrated demo booking with CAPTCHA verification
- **Support**: Help center and documentation
- **Blog**: Industry insights and company updates
- **Legal**: Privacy policy, terms of service, cookie policy, accessibility statement, security policy

## 🛠️ Tech Stack

| Category | Technologies |
|----------|-------------|
| **Frontend** | React 18, TypeScript 5, Tailwind CSS |
| **Build Tool** | Vite 5 with optimized configuration |
| **UI Components** | Radix UI, Lucide React icons |
| **Forms** | React Hook Form, Google reCAPTCHA |
| **Routing** | React Router DOM v6 |
| **Styling** | Tailwind CSS with custom animations |
| **State Management** | React Context API |
| **Type Safety** | Full TypeScript implementation |
| **Deployment** | Vercel-ready with edge functions |

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ and npm
- Git

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/eria-software/website.git
   cd eria-software-website
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment setup**
   ```bash
   cp .env.example .env.local
   ```

   Configure your environment variables:
   ```env
   # reCAPTCHA (required for forms)
   VITE_RECAPTCHA_SITE_KEY=your_recaptcha_site_key_here

   # Optional: Analytics
   VITE_GA_TRACKING_ID=your_google_analytics_id
   ```

4. **Start development server**
   ```bash
   npm run dev
   ```

5. **Open in browser**
   ```
   http://localhost:5173
   ```

## 📜 Available Scripts

| Command | Description |
|---------|-------------|
| `npm run dev` | Start development server with hot reload |
| `npm run build` | Build optimized production bundle |
| `npm run preview` | Preview production build locally |
| `npm run lint` | Run ESLint for code quality |
| `npm run lint:fix` | Fix auto-fixable ESLint issues |
| `npm run type-check` | Run TypeScript type checking |
| `npm run format` | Format code with Prettier |

## 🔧 reCAPTCHA Configuration

**Current Status:** Forms show "This reCAPTCHA is for testing purposes only"

**To Fix:**
1. **Get reCAPTCHA keys:** Visit [Google reCAPTCHA Admin](https://www.google.com/recaptcha/admin)
2. **Follow setup guide:** See [RECAPTCHA_SETUP.md](./RECAPTCHA_SETUP.md)
3. **Add site key:** Update `VITE_RECAPTCHA_SITE_KEY` in `.env.local`
4. **Restart server:** The test message will disappear

## 💻 Development

### Available Scripts

```bash
# Development server with hot reload
npm run dev

# Build for production
npm run build

# Build for development (with source maps)
npm run build:dev

# Preview production build locally
npm run preview

# Lint code
npm run lint
```

### Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── ui/             # shadcn/ui components
│   ├── Header.tsx      # Navigation header
│   ├── Footer.tsx      # Site footer
│   └── ...
├── pages/              # Page components
│   ├── Index.tsx       # Home page
│   ├── PointOfSale.tsx # POS page
│   └── ...
├── lib/                # Utility libraries
│   ├── utils.ts        # General utilities
│   ├── security.ts     # Security utilities
│   ├── seo.ts          # SEO utilities
│   └── ...
├── hooks/              # Custom React hooks
├── assets/             # Static assets
└── main.tsx           # Application entry point
```

### Code Style

- **TypeScript**: Strict mode enabled
- **ESLint**: Configured for React and TypeScript
- **Prettier**: Code formatting (if configured)
- **Tailwind CSS**: Utility-first styling approach

### Environment Variables

Create a `.env.local` file with the following variables:

```env
# Application
VITE_APP_NAME=ERIA POS
VITE_BASE_URL=https://eriasoftware.com

# API Configuration
VITE_API_URL=https://api.eriasoftware.com

# Feature Flags
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_ERROR_REPORTING=true

# Contact Information
VITE_CONTACT_PHONE=+91-**********
VITE_CONTACT_EMAIL=<EMAIL>

# Security (Optional - for production)
VITE_RECAPTCHA_SITE_KEY=your_recaptcha_site_key_here
```

## 🔒 Security Features

### CAPTCHA Protection
- **Google reCAPTCHA v2**: Integrated on contact and demo forms to prevent spam and automated submissions
- **Site Key Configuration**: Uses test key by default, replace with production key for live deployment
- **Form Validation**: CAPTCHA verification required before form submission

### Form Security
- **Formspree Integration**: Secure form handling with built-in spam protection
- **Input Validation**: Client-side and server-side validation for all form inputs
- **Error Handling**: Graceful error handling with user-friendly messages

### Cookie Management
- **GDPR Compliance**: Cookie consent popup with accept/decline options
- **Local Storage**: User preferences stored locally for consent management
- **Privacy Policy**: Comprehensive cookie policy page

### Additional Security
- **XSS Protection**: Input sanitization and content security policies
- **HTTPS Enforcement**: Secure connections for all production deployments
- **Environment Variables**: Sensitive data stored in environment variables

## 🚀 Deployment

### Production Build

```bash
# Create optimized production build
npm run build

# The build files will be in the `dist/` directory
```

### Deployment Options

#### 1. Netlify (Recommended)
1. Connect your GitHub repository to Netlify
2. Set build command: `npm run build`
3. Set publish directory: `dist`
4. Deploy automatically on push to main branch

#### 2. Vercel
1. Import project from GitHub
2. Vercel will auto-detect Vite configuration
3. Deploy with zero configuration

#### 3. Traditional Web Hosting
1. Run `npm run build`
2. Upload `dist/` folder contents to your web server
3. Configure server for SPA routing (redirect all routes to index.html)

### Server Configuration

For SPA routing, configure your server to serve `index.html` for all routes:

**Nginx**:
```nginx
location / {
    try_files $uri $uri/ /index.html;
}
```

**Apache** (.htaccess):
```apache
RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule . /index.html [L]
```

## 🔒 Security

### Security Features Implemented

- **Content Security Policy (CSP)**: Prevents XSS attacks
- **Security Headers**: X-Frame-Options, X-Content-Type-Options, etc.
- **Input Sanitization**: All user inputs are sanitized
- **HTTPS Enforcement**: Strict Transport Security headers
- **Error Handling**: Secure error messages without sensitive data

### Security Best Practices

- Regular dependency updates
- Environment variable protection
- Secure API communication
- Input validation and sanitization
- Error boundary implementation

## ⚡ Performance

### Performance Optimizations

- **Code Splitting**: Route-based lazy loading
- **Tree Shaking**: Unused code elimination
- **Image Optimization**: Lazy loading and responsive images
- **Caching**: Proper cache headers for static assets
- **Minification**: CSS and JavaScript minification
- **Gzip Compression**: Server-side compression

### Performance Monitoring

- Web Vitals tracking
- Core Web Vitals optimization
- Performance budgets
- Bundle size analysis

## 🤝 Contributing

We welcome contributions! Please follow these steps:

1. **Fork the repository**
2. **Create a feature branch**: `git checkout -b feature/amazing-feature`
3. **Commit changes**: `git commit -m 'Add amazing feature'`
4. **Push to branch**: `git push origin feature/amazing-feature`
5. **Open a Pull Request**

### Development Guidelines

- Follow TypeScript best practices
- Write meaningful commit messages
- Add tests for new features
- Update documentation as needed
- Follow the existing code style

## 🆕 Recent Updates

### Version 2.0.0 (Latest)
- ✅ **CAPTCHA Protection**: Added Google reCAPTCHA to contact and demo forms
- ✅ **Loading Spinners**: Implemented elegant loading states across all pages
- ✅ **Demo Page**: New dedicated demo booking page with Cal.com integration
- ✅ **Cookie Consent**: GDPR-compliant cookie consent popup
- ✅ **Form Enhancements**: Improved contact form with Formspree integration
- ✅ **Error Handling**: Enhanced error handling and user feedback
- ✅ **Trust Indicators**: Updated restaurant count to 100+ for accuracy
- ✅ **Product Suite**: Updated to match available services (Website Dev, App Dev, CRM)
- ✅ **Toast Notifications**: Real-time feedback for all user actions
- ✅ **Security Improvements**: Enhanced form security and spam protection

### Previous Updates
- 🔧 **Performance Optimization**: Improved loading times and code splitting
- 🎨 **UI/UX Enhancements**: Modern design with better accessibility
- 📱 **Mobile Responsiveness**: Enhanced mobile experience
- 🔍 **SEO Optimization**: Comprehensive SEO implementation

## 📞 Support

### Getting Help

- **Documentation**: Check this README and inline code comments
- **Issues**: Create a GitHub issue for bugs or feature requests
- **Email**: <EMAIL>
- **Phone**: +91-**********

### Business Inquiries

For business inquiries, demos, or custom development:
- **Email**: <EMAIL>
- **Website**: https://eriasoftware.com
- **Phone**: +91-**********

## 📄 License

This project is proprietary software owned by **Eria Software Solutions & Services Pvt Ltd**. All rights reserved.

## 🙏 Acknowledgments

- **React Team** - For the amazing React framework
- **Vite Team** - For the fast build tool
- **Tailwind CSS** - For the utility-first CSS framework
- **shadcn/ui** - For the beautiful component library

---

**Built with ❤️ by Eria Software Solutions & Services Pvt Ltd**

For more information, visit [eriasoftware.com](https://eriasoftware.com)
