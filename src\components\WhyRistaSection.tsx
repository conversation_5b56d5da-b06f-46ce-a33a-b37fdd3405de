import {
  Clock,
  ShoppingBag,
  Recycle,
  TrendingUp,
  Heart,
  Zap,
  Headphones,
  Shield,
  ArrowUpRight,
  Smartphone,
  RefreshCcw
} from "lucide-react";

const WhyRistaSection = () => {
  const benefits = [
    {
      icon: Clock,
      title: "विजेसारखी जलद सेवा",
      description: "प्रगत ऑर्डर प्रक्रिया आणि एकीकृत किचन डिस्प्लेसह सेवा वेळ 40% कमी करा."
    },
    {
      icon: ShoppingBag,
      title: "स्मार्ट इन्व्हेंटरी नियंत्रण",
      description: "स्वयंचलित अलर्ट आणि भविष्यसूचक पुनर्ऑर्डरिंग 25% खर्च कमी करताना स्टॉकआउट्स टाळतात."
    },
    {
      icon: Recycle,
      title: "कचरा कमी करणे",
      description: "गुणवत्ता राखताना अचूक ट्रॅकिंग आणि विश्लेषण अन्न कचरा 25% कमी करते."
    },
    {
      icon: TrendingUp,
      title: "महसूल वाढ",
      description: "AI-संचालित अपसेलिंग आणि WhatsApp मार्केटिंग महसूल 20-30% वाढवते."
    },
    {
      icon: Heart,
      title: "ग्राहक निष्ठा",
      description: "वैयक्तिकृत गुंतवणूक साधने पुनरावृत्ती भेटी 35% वाढवतात आणि जीवनकाळ मूल्य वाढवतात."
    },
    {
      icon: Zap,
      title: "सोपे एकीकरण",
      description: "निर्बाध कामकाजासाठी आमच्या मजबूत API द्वारे 50+ व्यवसाय साधनांशी कनेक्ट करा."
    },
    {
      icon: Clock,
      title: "जलद सेटअप",
      description: "सहज इंटरफेस आणि व्यापक प्रशिक्षणासह 24 तासांत कार्यरत व्हा."
    },
    {
      icon: Headphones,
      title: "24/7 समर्थन",
      description: "2-मिनिट प्रतिसाद वेळ आणि 99.9% समस्या निराकरण दरासह तज्ञ समर्थन."
    },
    {
      icon: Shield,
      title: "एंटरप्राइझ सुरक्षा",
      description: "लष्करी-दर्जाचे एन्क्रिप्शन आणि PCI DSS अनुपालन आपला संवेदनशील डेटा संरक्षित करते."
    },
    {
      icon: ArrowUpRight,
      title: "अमर्यादित स्केलेबिलिटी",
      description: "क्लाउड-आधारित आर्किटेक्चर एकल स्थानापासून मल्टी-चेन ऑपरेशन्सपर्यंत वाढते."
    },
    {
      icon: RefreshCcw,
      title: "नियमित अपडेट्स",
      description: "मासिक वैशिष्ट्य रिलीज आपल्याला अत्याधुनिक क्षमतांसह पुढे ठेवतात."
    },
    {
      icon: Smartphone,
      title: "मोबाइल व्यवस्थापन",
      description: "व्यापक मोबाइल अॅप कार्यक्षमतेसह कुठेही ऑपरेशन्स नियंत्रित करा."
    }
  ];

  return (
    <section className="w-full py-16 lg:py-24 bg-muted/20">
      <div className="container mx-auto px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="text-sm font-semibold text-primary mb-4 uppercase tracking-wide">
            का Zyka POS?
          </div>
          <h2 className="text-3xl lg:text-5xl font-bold text-foreground mb-4">
            परिणाम देणारे संपूर्ण रेस्टॉरंट व्यवस्थापन समाधान
          </h2>
        </div>

        {/* Benefits Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
          {benefits.map((benefit, index) => (
            <div 
              key={index} 
              className="bg-background rounded-lg p-6 shadow-soft hover:shadow-lg transition-all duration-300 hover:scale-105"
            >
              <div className="space-y-4">
                <div className="w-12 h-12 bg-gradient-primary rounded-lg flex items-center justify-center">
                  <benefit.icon className="w-6 h-6 text-primary-foreground" />
                </div>
                
                <h3 className="text-lg font-semibold text-foreground">
                  {benefit.title}
                </h3>
                
                <p className="text-sm text-muted-foreground leading-relaxed">
                  {benefit.description}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default WhyRistaSection;