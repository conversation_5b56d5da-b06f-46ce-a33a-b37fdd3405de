// Security utilities and validation functions

export interface SecurityConfig {
  enableCSP: boolean;
  enableHSTS: boolean;
  enableXFrameOptions: boolean;
  enableXContentTypeOptions: boolean;
  enableReferrerPolicy: boolean;
}

export const defaultSecurityConfig: SecurityConfig = {
  enableCSP: true,
  enableHSTS: true,
  enableXFrameOptions: true,
  enableXContentTypeOptions: true,
  enableReferrerPolicy: true
};

// Input validation and sanitization
export class InputValidator {
  // Email validation with comprehensive regex
  static validateEmail(email: string): boolean {
    const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
    return emailRegex.test(email) && email.length <= 254;
  }

  // Phone number validation (international format)
  static validatePhone(phone: string): boolean {
    const phoneRegex = /^\+?[1-9]\d{1,14}$/;
    const cleanPhone = phone.replace(/[\s\-\(\)]/g, '');
    return phoneRegex.test(cleanPhone) && cleanPhone.length >= 7 && cleanPhone.length <= 15;
  }

  // Name validation (letters, spaces, hyphens, apostrophes)
  static validateName(name: string): boolean {
    const nameRegex = /^[a-zA-Z\s\-']{2,50}$/;
    return nameRegex.test(name.trim());
  }

  // Subject validation
  static validateSubject(subject: string): boolean {
    const allowedSubjects = ['demo', 'support', 'sales', 'partnership', 'other'];
    return allowedSubjects.includes(subject);
  }

  // Message validation (prevent XSS and injection)
  static validateMessage(message: string): boolean {
    if (!message || message.trim().length < 10 || message.length > 2000) {
      return false;
    }
    
    // Check for potential XSS patterns
    const xssPatterns = [
      /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
      /javascript:/gi,
      /on\w+\s*=/gi,
      /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi,
      /<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/gi,
      /<embed\b[^<]*(?:(?!<\/embed>)<[^<]*)*<\/embed>/gi
    ];
    
    return !xssPatterns.some(pattern => pattern.test(message));
  }

  // Sanitize input to prevent XSS
  static sanitizeInput(input: string): string {
    return input
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;')
      .replace(/\//g, '&#x2F;');
  }

  // Rate limiting check (simple client-side)
  static checkRateLimit(key: string, maxAttempts: number = 5, windowMs: number = 300000): boolean {
    const now = Date.now();
    const attempts = JSON.parse(localStorage.getItem(`rate_limit_${key}`) || '[]');
    
    // Remove old attempts outside the window
    const validAttempts = attempts.filter((timestamp: number) => now - timestamp < windowMs);
    
    if (validAttempts.length >= maxAttempts) {
      return false;
    }
    
    // Add current attempt
    validAttempts.push(now);
    localStorage.setItem(`rate_limit_${key}`, JSON.stringify(validAttempts));
    
    return true;
  }
}

// Form security utilities
export class FormSecurity {
  // Generate CSRF token
  static generateCSRFToken(): string {
    const array = new Uint8Array(32);
    crypto.getRandomValues(array);
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  }

  // Validate form data comprehensively
  static validateContactForm(data: {
    firstName: string;
    lastName: string;
    email: string;
    phone?: string;
    subject: string;
    message: string;
  }): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!InputValidator.validateName(data.firstName)) {
      errors.push('First name must be 2-50 characters and contain only letters, spaces, hyphens, and apostrophes');
    }

    if (!InputValidator.validateName(data.lastName)) {
      errors.push('Last name must be 2-50 characters and contain only letters, spaces, hyphens, and apostrophes');
    }

    if (!InputValidator.validateEmail(data.email)) {
      errors.push('Please enter a valid email address');
    }

    if (data.phone && !InputValidator.validatePhone(data.phone)) {
      errors.push('Please enter a valid phone number');
    }

    if (!InputValidator.validateSubject(data.subject)) {
      errors.push('Please select a valid subject');
    }

    if (!InputValidator.validateMessage(data.message)) {
      errors.push('Message must be 10-2000 characters and contain no malicious content');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // Check for suspicious patterns
  static detectSuspiciousActivity(data: any): boolean {
    const suspiciousPatterns = [
      /\b(union|select|insert|delete|drop|create|alter|exec|execute)\b/gi,
      /\b(script|javascript|vbscript|onload|onerror|onclick)\b/gi,
      /<[^>]*>/g, // HTML tags
      /\b(http|https|ftp):\/\/[^\s]+/gi // URLs in unexpected places
    ];

    const dataString = JSON.stringify(data).toLowerCase();
    return suspiciousPatterns.some(pattern => pattern.test(dataString));
  }
}

// Security headers management
export class SecurityHeaders {
  static setSecurityHeaders(config: SecurityConfig = defaultSecurityConfig): void {
    if (typeof document === 'undefined') return;

    // Content Security Policy
    if (config.enableCSP) {
      const cspMeta = document.createElement('meta');
      cspMeta.httpEquiv = 'Content-Security-Policy';
      cspMeta.content = [
        "default-src 'self'",
        "script-src 'self' 'unsafe-inline' https://www.google.com https://www.gstatic.com https://cal.com",
        "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
        "font-src 'self' https://fonts.gstatic.com",
        "img-src 'self' data: https:",
        "connect-src 'self' https://formspree.io https://cal.com",
        "frame-src https://www.google.com https://cal.com",
        "object-src 'none'",
        "base-uri 'self'"
      ].join('; ');
      document.head.appendChild(cspMeta);
    }

    // X-Frame-Options
    if (config.enableXFrameOptions) {
      const frameMeta = document.createElement('meta');
      frameMeta.httpEquiv = 'X-Frame-Options';
      frameMeta.content = 'DENY';
      document.head.appendChild(frameMeta);
    }

    // X-Content-Type-Options
    if (config.enableXContentTypeOptions) {
      const contentTypeMeta = document.createElement('meta');
      contentTypeMeta.httpEquiv = 'X-Content-Type-Options';
      contentTypeMeta.content = 'nosniff';
      document.head.appendChild(contentTypeMeta);
    }

    // Referrer Policy
    if (config.enableReferrerPolicy) {
      const referrerMeta = document.createElement('meta');
      referrerMeta.name = 'referrer';
      referrerMeta.content = 'strict-origin-when-cross-origin';
      document.head.appendChild(referrerMeta);
    }
  }
}

// Data protection utilities
export class DataProtection {
  // Encrypt sensitive data before storage
  static encryptData(data: string, key?: string): string {
    // Simple encryption for client-side (not for highly sensitive data)
    const encoder = new TextEncoder();
    const dataArray = encoder.encode(data);
    const keyArray = encoder.encode(key || 'eria-software-key');
    
    const encrypted = dataArray.map((byte, index) => 
      byte ^ keyArray[index % keyArray.length]
    );
    
    return btoa(String.fromCharCode(...encrypted));
  }

  // Decrypt data
  static decryptData(encryptedData: string, key?: string): string {
    try {
      const decoder = new TextDecoder();
      const encoder = new TextEncoder();
      const keyArray = encoder.encode(key || 'eria-software-key');
      
      const encrypted = new Uint8Array(
        atob(encryptedData).split('').map(char => char.charCodeAt(0))
      );
      
      const decrypted = encrypted.map((byte, index) => 
        byte ^ keyArray[index % keyArray.length]
      );
      
      return decoder.decode(decrypted);
    } catch {
      return '';
    }
  }

  // Secure data deletion
  static secureDelete(key: string): void {
    if (typeof localStorage !== 'undefined') {
      localStorage.removeItem(key);
    }
    if (typeof sessionStorage !== 'undefined') {
      sessionStorage.removeItem(key);
    }
  }
}

// Initialize security measures
export const initializeSecurity = (): void => {
  SecurityHeaders.setSecurityHeaders();
  
  // Disable right-click context menu in production
  if (import.meta.env.PROD) {
    document.addEventListener('contextmenu', (e) => e.preventDefault());
  }
  
  // Disable F12 and other developer shortcuts in production
  if (import.meta.env.PROD) {
    document.addEventListener('keydown', (e) => {
      if (e.key === 'F12' || 
          (e.ctrlKey && e.shiftKey && e.key === 'I') ||
          (e.ctrlKey && e.shiftKey && e.key === 'C') ||
          (e.ctrlKey && e.key === 'U')) {
        e.preventDefault();
      }
    });
  }
};
