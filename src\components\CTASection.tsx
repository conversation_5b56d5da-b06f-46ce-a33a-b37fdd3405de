import { Button } from "@/components/ui/button";
import { ArrowRight, Phone } from "lucide-react";

const CTASection = () => {
  return (
    <section className="w-full py-16 lg:py-24 bg-black">
      <div className="container mx-auto px-6 lg:px-8 text-center">
        <div className="max-w-3xl mx-auto space-y-8">
          <h2 className="text-3xl lg:text-5xl font-bold text-white">
            आजच आपल्या रेस्टॉरंट सुरू करा 
          </h2>

          <p className="text-lg lg:text-xl text-white/90 leading-relaxed">
            <PERSON><PERSON><PERSON> POS सह त्यांचा महसूल 30% वाढवणार्‍या आणि परिचालन खर्च 25% कमी करणार्‍या 100+ यशस्वी रेस्टॉरंट्समध्ये सामील व्हा.
            बुद्धिमान रेस्टॉरंट व्यवस्थापन तंत्रज्ञान आपल्या व्यवसायाच्या वाढीसाठी आणि ग्राहक समाधानासाठी कसा फरक करू शकतो याचा अनुभव घ्या.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Button
              variant="hero"
              size="lg"
              className="text-lg px-8 py-6 bg-blue-600 text-white hover:bg-blue-700"
              onClick={() => window.open('https://zykapos.eriasoftware.com', '_blank')}
            >
              मोफत डेमो घ्या
              <ArrowRight className="w-5 h-5" />
            </Button>

            <Button
              variant="outline"
              size="lg"
              className="text-lg px-8 py-6 bg-transparent border-2 border-white text-white hover:bg-white hover:text-black"
            >
              <Phone className="w-5 h-5" />
              आत्ताच कॉल करा
            </Button>
          </div>

          <div className="pt-8 text-sm text-white/80">
            ✓ मोफत सेटअप आणि प्रशिक्षण • ✓ 30-दिवसांची पैसे परत करण्याची हमी • ✓ 24/7 समर्थन समाविष्ट
          </div>
        </div>
      </div>
    </section>
  );
};

export default CTASection;