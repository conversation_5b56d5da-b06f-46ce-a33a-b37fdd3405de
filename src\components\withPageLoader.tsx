import React from "react";
import { usePageLoading } from "@/hooks/use-page-loading";
import PageLoader from "@/components/PageLoader";

interface WithPageLoaderOptions {
  loadingText?: string;
  minLoadingTime?: number;
  initialDelay?: number;
}

const withPageLoader = <P extends object>(
  WrappedComponent: React.ComponentType<P>,
  options: WithPageLoaderOptions = {}
) => {
  const WithPageLoaderComponent = (props: P) => {
    const { loadingText = "Loading page...", minLoadingTime, initialDelay } = options;
    const { isLoading } = usePageLoading({ minLoadingTime, initialDelay });

    if (isLoading) {
      return <PageLoader text={loadingText} />;
    }

    return <WrappedComponent {...props} />;
  };

  WithPageLoaderComponent.displayName = `withPageLoader(${WrappedComponent.displayName || WrappedComponent.name})`;

  return WithPageLoaderComponent;
};

export default withPageLoader;
