import { Facebook, Instagram, <PERSON>edin, Twitter } from "lucide-react";
import { Link } from "react-router-dom";

const Footer = () => {
  return (
    <footer className="w-full bg-black border-t border-gray-800 py-12">
      <div className="container mx-auto px-6 lg:px-8">
        <div className="grid md:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="space-y-4">
            <h3 className="text-xl font-bold text-white">रिया सॉफ्टवेअर</h3>
            <p className="text-gray-300 text-sm leading-relaxed">
              संपूर्ण भारतातील रेस्टॉरंट्स, कॅफे आणि व्यवसायांसाठी Zyka POS सॉफ्टवेअर आणि व्यापक IT समाधानांचे आघाडीचे प्रदाता.
              50+ आनंदी ग्राहकांचा विश्वास.
            </p>
            <div className="space-y-2 mt-4">
              <p className="text-gray-300 text-sm">
                <a href="mailto:<EMAIL>" className="hover:text-white transition-colors">
                  <EMAIL>
                </a>
              </p>
              <p className="text-gray-300 text-sm">
                <a href="tel:+919604069989" className="hover:text-white transition-colors">
                  +91 9604069989
                </a>
              </p>
              <p className="text-gray-300 text-sm">मुंबई, महाराष्ट्र</p>
            </div>
          </div>

          {/* Product Links */}
          <div className="space-y-4">
            <h4 className="font-semibold text-white">उत्पादने</h4>
            <ul className="space-y-2 text-sm">
              <li><a href="https://zykapos.eriasoftware.com" target="_blank" rel="noopener noreferrer" className="text-gray-300 hover:text-white transition-colors">पॉइंट ऑफ सेल</a></li>
              <li><Link to="/inventory" className="text-gray-300 hover:text-white transition-colors">इन्व्हेंटरी व्यवस्थापन</Link></li>
              <li><Link to="/" className="text-gray-300 hover:text-white transition-colors">Zyka POS सॉफ्टवेअर</Link></li>
            </ul>
          </div>

          {/* Services Links */}
          <div className="space-y-4">
            <h4 className="font-semibold text-white">सेवा</h4>
            <ul className="space-y-2 text-sm">
              <li><Link to="/website-development" className="text-gray-300 hover:text-white transition-colors">वेबसाइट डेव्हलपमेंट</Link></li>
              <li><Link to="/app-development" className="text-gray-300 hover:text-white transition-colors">अॅप डेव्हलपमेंट</Link></li>
              <li><Link to="/freshservice-crm" className="text-gray-300 hover:text-white transition-colors">फ्रेशसर्व्हिस CRM</Link></li>
              <li><Link to="/contact" className="text-gray-300 hover:text-white transition-colors">संपर्क करा</Link></li>
              <li><Link to="/support" className="text-gray-300 hover:text-white transition-colors">समर्थन</Link></li>
            </ul>
          </div>

          {/* Company Links */}
          <div className="space-y-4">
            <h4 className="font-semibold text-white">कंपनी</h4>
            <ul className="space-y-2 text-sm">
              <li><Link to="/about-us" className="text-gray-300 hover:text-white transition-colors">आमच्याबद्दल</Link></li>
              <li><Link to="/blog" className="text-gray-300 hover:text-white transition-colors">ब्लॉग</Link></li>
              <li><Link to="/privacy-policy" className="text-gray-300 hover:text-white transition-colors">गोपनीयता धोरण</Link></li>
              <li><Link to="/terms-of-service" className="text-gray-300 hover:text-white transition-colors">सेवा अटी</Link></li>
              <li><Link to="/accessibility-statement" className="text-gray-300 hover:text-white transition-colors">प्रवेशयोग्यता</Link></li>
              <li><Link to="/security-policy" className="text-gray-300 hover:text-white transition-colors">सुरक्षा</Link></li>
            </ul>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-800 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-sm text-gray-300">
            © 2024 Eria Software Solutions and Services Pvt Ltd. सर्व हक्क राखीव.
          </p>
          <div className="flex flex-col md:flex-row items-center gap-4 mt-4 md:mt-0">
            <div className="flex flex-wrap space-x-4 text-sm">
              <Link to="/terms-of-service" className="text-gray-300 hover:text-white transition-colors">
                अटी
              </Link>
              <Link to="/privacy-policy" className="text-gray-300 hover:text-white transition-colors">
                गोपनीयता
              </Link>
              <Link to="/cookie-policy" className="text-gray-300 hover:text-white transition-colors">
                कुकीज
              </Link>
              <Link to="/accessibility-statement" className="text-gray-300 hover:text-white transition-colors">
                प्रवेशयोग्यता
              </Link>
              <Link to="/security-policy" className="text-gray-300 hover:text-white transition-colors">
                सुरक्षा
              </Link>
            </div>

            {/* Social Media Icons */}
            <div className="flex space-x-4">
              <a
                href="https://linkedin.com/company/eria-software"
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-300 hover:text-white transition-colors"
              >
                <Linkedin className="w-5 h-5" />
              </a>
              <a
                href="https://instagram.com/eriasoftware"
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-300 hover:text-white transition-colors"
              >
                <Instagram className="w-5 h-5" />
              </a>
              <a
                href="https://facebook.com/eriasoftware"
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-300 hover:text-white transition-colors"
              >
                <Facebook className="w-5 h-5" />
              </a>
              <a
                href="https://x.com/eriasoftware"
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-300 hover:text-white transition-colors"
              >
                <Twitter className="w-5 h-5" />
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;