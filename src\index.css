@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
:root {
    --background: 0 0% 100%;
    --foreground: 0 0% 0%;

    --card: 0 0% 100%;
    --card-foreground: 0 0% 0%;

    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 0%;

    --primary: 220 100% 55%;
    --primary-foreground: 0 0% 100%;

    --secondary: 0 0% 96%;
    --secondary-foreground: 0 0% 20%;

    --muted: 0 0% 96%;
    --muted-foreground: 0 0% 45%;

    --accent: 0 0% 96%;
    --accent-foreground: 0 0% 20%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 100%;

    --border: 0 0% 90%;
    --input: 0 0% 90%;
    --ring: 220 100% 55%;

    --gradient-primary: linear-gradient(135deg, hsl(220, 100%, 55%), hsl(220, 100%, 65%));
    --gradient-hero: linear-gradient(135deg, hsl(0, 0%, 98%), hsl(0, 0%, 96%));
    --shadow-soft: 0 4px 20px hsl(0, 0%, 0%, 0.1);
    --shadow-button: 0 8px 25px hsl(220, 100%, 55%, 0.2);

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

.dark {
    --background: 220.9 39.3% 11%;
    --foreground: 210 40% 98%;

    --card: 220.9 39.3% 11%;
    --card-foreground: 210 40% 98%;

    --popover: 220.9 39.3% 11%;
    --popover-foreground: 210 40% 98%;

    --primary: 220 100% 55%;
    --primary-foreground: 220.9 39.3% 11%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 220 100% 55%;

    --gradient-primary: linear-gradient(135deg, hsl(220, 100%, 55%), hsl(220, 100%, 65%));
    --gradient-hero: linear-gradient(135deg, hsl(217.2, 32.6%, 17.5%), hsl(217.2, 32.6%, 15%));
    --shadow-soft: 0 4px 20px hsl(220, 100%, 55%, 0.2);
    --shadow-button: 0 8px 25px hsl(220, 100%, 55%, 0.3);
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-family: 'Poppins', sans-serif;
  }

  /* Custom animations */
  @keyframes marquee {
    0% {
      transform: translateX(100%);
    }
    100% {
      transform: translateX(-100%);
    }
  }

  @keyframes shimmer {
    0% {
      transform: translateX(-100%) skewX(-12deg);
    }
    100% {
      transform: translateX(200%) skewX(-12deg);
    }
  }

  @keyframes fade-in-out {
    0%, 100% {
      opacity: 0;
      transform: translateY(10px);
    }
    10%, 90% {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-marquee {
    animation: marquee 30s linear infinite;
  }

  .animate-marquee-fast {
    animation: marquee 15s linear infinite;
  }

  .animate-marquee-fast {
    animation: marquee 15s linear infinite;
  }

  .animate-shimmer {
    animation: shimmer 2s infinite;
  }

  .animate-fade-in-out {
    animation: fade-in-out 4s ease-in-out infinite;
  }

  /* Smooth scroll behavior */
  html {
    scroll-behavior: smooth;
  }

  /* Hover effects for interactive elements */
  .hover-lift {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }

  .hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  }

  /* Gradient text animation */
  @keyframes gradient-shift {
    0%, 100% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
  }

  .animate-gradient {
    background: linear-gradient(-45deg, #3b82f6, #8b5cf6, #06b6d4, #10b981);
    background-size: 400% 400%;
    animation: gradient-shift 3s ease infinite;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Floating animation */
  @keyframes float {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px);
    }
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }
}