import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// Global gtag declaration
declare global {
  interface Window {
    gtag?: (...args: any[]) => void;
  }
}

// Cookie Management Types
export interface CookieCategory {
  id: string;
  name: string;
  description: string;
  required: boolean;
  cookies: CookieInfo[];
}

export interface CookieInfo {
  name: string;
  purpose: string;
  duration: string;
  type: 'session' | 'persistent';
  provider: string;
  category: string;
}

export interface CookieConsent {
  essential: boolean;
  functional: boolean;
  analytics: boolean;
  marketing: boolean;
  timestamp: number;
  version: string;
}

// Cookie Categories Configuration
export const COOKIE_CATEGORIES: CookieCategory[] = [
  {
    id: 'essential',
    name: 'Essential Cookies',
    description: 'These cookies are necessary for the website to function properly and cannot be disabled. They are usually set in response to actions made by you which amount to a request for services.',
    required: true,
    cookies: [
      {
        name: 'cookieConsent',
        purpose: 'Stores your cookie consent preferences',
        duration: '1 year',
        type: 'persistent',
        provider: 'Eria Software',
        category: 'essential'
      },
      {
        name: 'sessionId',
        purpose: 'Maintains your session while browsing',
        duration: 'Session',
        type: 'session',
        provider: 'Eria Software',
        category: 'essential'
      }
    ]
  },
  {
    id: 'functional',
    name: 'Functional Cookies',
    description: 'These cookies enable enhanced functionality and personalization. They may be set by us or by third party providers whose services we have added to our pages.',
    required: false,
    cookies: [
      {
        name: 'userPreferences',
        purpose: 'Remembers your preferences and settings',
        duration: '6 months',
        type: 'persistent',
        provider: 'Eria Software',
        category: 'functional'
      },
      {
        name: 'language',
        purpose: 'Stores your preferred language',
        duration: '1 year',
        type: 'persistent',
        provider: 'Eria Software',
        category: 'functional'
      }
    ]
  },
  {
    id: 'analytics',
    name: 'Analytics Cookies',
    description: 'These cookies help us understand how visitors interact with our website by collecting and reporting information anonymously.',
    required: false,
    cookies: [
      {
        name: '_ga',
        purpose: 'Used to distinguish users for Google Analytics',
        duration: '2 years',
        type: 'persistent',
        provider: 'Google',
        category: 'analytics'
      },
      {
        name: '_ga_*',
        purpose: 'Used to persist session state for Google Analytics',
        duration: '2 years',
        type: 'persistent',
        provider: 'Google',
        category: 'analytics'
      }
    ]
  },
  {
    id: 'marketing',
    name: 'Marketing Cookies',
    description: 'These cookies are used to deliver relevant advertisements and track the effectiveness of our advertising campaigns.',
    required: false,
    cookies: [
      {
        name: 'fbp',
        purpose: 'Facebook pixel for tracking conversions',
        duration: '3 months',
        type: 'persistent',
        provider: 'Facebook',
        category: 'marketing'
      },
      {
        name: 'ads_data',
        purpose: 'Stores advertising preferences and targeting data',
        duration: '30 days',
        type: 'persistent',
        provider: 'Various',
        category: 'marketing'
      }
    ]
  }
];

// Cookie Management Utilities
export class CookieManager {
  private static readonly CONSENT_KEY = 'cookieConsent';
  private static readonly CONSENT_VERSION = '1.0';

  static getConsent(): CookieConsent | null {
    try {
      const stored = localStorage.getItem(this.CONSENT_KEY);
      if (!stored) return null;
      return JSON.parse(stored);
    } catch {
      return null;
    }
  }

  static setConsent(consent: Partial<CookieConsent>): void {
    const fullConsent: CookieConsent = {
      essential: true, // Always true
      functional: consent.functional ?? false,
      analytics: consent.analytics ?? false,
      marketing: consent.marketing ?? false,
      timestamp: Date.now(),
      version: this.CONSENT_VERSION
    };

    localStorage.setItem(this.CONSENT_KEY, JSON.stringify(fullConsent));
    this.applyCookieSettings(fullConsent);

    // Record compliance action (if compliance module is available)
    try {
      // Dynamic import to avoid circular dependencies
      import('./cookieCompliance').then(({ CookieComplianceManager }) => {
        CookieComplianceManager.recordConsentAction('granted', 'all', fullConsent);
      });
    } catch {
      // Compliance module not available
    }
  }

  static hasConsent(): boolean {
    return this.getConsent() !== null;
  }

  static needsConsentUpdate(): boolean {
    const consent = this.getConsent();
    if (!consent) return true;
    return consent.version !== this.CONSENT_VERSION;
  }

  static clearConsent(): void {
    localStorage.removeItem(this.CONSENT_KEY);
  }

  private static applyCookieSettings(consent: CookieConsent): void {
    // Apply analytics cookies
    if (consent.analytics) {
      this.enableAnalytics();
    } else {
      this.disableAnalytics();
    }

    // Apply marketing cookies
    if (consent.marketing) {
      this.enableMarketing();
    } else {
      this.disableMarketing();
    }

    // Functional cookies are handled by the application
  }

  private static enableAnalytics(): void {
    // Enable Google Analytics
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('consent', 'update', {
        analytics_storage: 'granted'
      });
    }
  }

  private static disableAnalytics(): void {
    // Disable Google Analytics
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('consent', 'update', {
        analytics_storage: 'denied'
      });
    }

    // Clear existing analytics cookies
    this.clearCookiesByCategory('analytics');
  }

  private static enableMarketing(): void {
    // Enable marketing cookies
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('consent', 'update', {
        ad_storage: 'granted',
        ad_user_data: 'granted',
        ad_personalization: 'granted'
      });
    }
  }

  private static disableMarketing(): void {
    // Disable marketing cookies
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('consent', 'update', {
        ad_storage: 'denied',
        ad_user_data: 'denied',
        ad_personalization: 'denied'
      });
    }

    // Clear existing marketing cookies
    this.clearCookiesByCategory('marketing');
  }

  private static clearCookiesByCategory(category: string): void {
    const categoryData = COOKIE_CATEGORIES.find(cat => cat.id === category);
    if (!categoryData) return;

    categoryData.cookies.forEach(cookie => {
      // Clear cookie from all possible domains and paths
      const domains = ['', '.eriasoftware.com', '.localhost'];
      const paths = ['/', '/app', '/admin'];

      domains.forEach(domain => {
        paths.forEach(path => {
          document.cookie = `${cookie.name}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=${path}; domain=${domain}`;
        });
      });
    });
  }

  static getCookieValue(name: string): string | null {
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) {
      return parts.pop()?.split(';').shift() || null;
    }
    return null;
  }

  static setCookie(name: string, value: string, days: number = 365): void {
    const expires = new Date();
    expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000));
    document.cookie = `${name}=${value}; expires=${expires.toUTCString()}; path=/; SameSite=Lax`;
  }
}
