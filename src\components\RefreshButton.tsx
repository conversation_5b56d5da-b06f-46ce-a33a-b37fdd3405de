import { But<PERSON> } from "@/components/ui/button";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { RefreshCw } from "lucide-react";
import { useRefresh } from "@/contexts/RefreshContext";
import { cn } from "@/lib/utils";

interface RefreshButtonProps {
  className?: string;
  size?: "sm" | "md" | "lg";
  variant?: "default" | "outline" | "ghost";
  showText?: boolean;
}

const RefreshButton = ({ 
  className, 
  size = "md", 
  variant = "outline",
  showText = true 
}: RefreshButtonProps) => {
  const { isRefreshing, refreshPage } = useRefresh();

  const sizeClasses = {
    sm: "px-3 py-2 text-sm",
    md: "px-4 py-2",
    lg: "px-6 py-3 text-lg"
  };

  return (
    <Button
      onClick={refreshPage}
      disabled={isRefreshing}
      variant={variant}
      className={cn(
        "inline-flex items-center space-x-2 transition-all duration-200",
        sizeClasses[size],
        className
      )}
    >
      {isRefreshing ? (
        <LoadingSpinner size="sm" />
      ) : (
        <RefreshCw className={cn(
          "transition-transform duration-200",
          size === "sm" ? "w-4 h-4" : size === "lg" ? "w-6 h-6" : "w-5 h-5"
        )} />
      )}
      {showText && (
        <span>{isRefreshing ? "Refreshing..." : "Refresh"}</span>
      )}
    </Button>
  );
};

export default RefreshButton;
