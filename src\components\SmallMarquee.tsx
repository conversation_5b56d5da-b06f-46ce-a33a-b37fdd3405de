import { Zap, Star, Award, TrendingUp, Users, Shield } from "lucide-react";

const SmallMarquee = () => {
  const highlights = [
    { icon: Star, text: "★ 4.9/5 ग्राहक रेटिंग" },
    { icon: Zap, text: "⚡ 30 सेकंदात सेटअप" },
    { icon: Award, text: "🏆 #1 रेस्टॉरंट POS" },
    { icon: TrendingUp, text: "📈 30% महसूल वाढ" },
    { icon: Users, text: "👥 100+ आनंदी ग्राहक" },
    { icon: Shield, text: "🔒 100% सुरक्षित" },
    { icon: Star, text: "✨ 24/7 समर्थन" },
    { icon: Zap, text: "🚀 मोफत डेमो" }
  ];

  return (
    <div className="bg-gradient-to-r from-green-500 via-blue-500 to-purple-500 py-2 overflow-hidden border-y border-gray-200">
      <div className="relative">
        {/* Marquee content */}
        <div className="flex animate-marquee-fast whitespace-nowrap">
          {highlights.concat(highlights).concat(highlights).map((item, index) => (
            <div
              key={index}
              className="flex items-center mx-6 text-white font-medium text-sm"
            >
              <span className="mr-2">{item.text}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default SmallMarquee;
